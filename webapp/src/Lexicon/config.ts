import type { GlobalConfig } from 'payload'

import { revalidateLexicon } from './hooks/revalidateLexicon'

export const Lexicon: GlobalConfig = {
  slug: 'lexicon',
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      defaultValue: 'Lexicon',
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      defaultValue: 'A comprehensive list of all terms and definitions.',
    },
    {
      name: 'meta',
      type: 'group',
      fields: [
        {
          name: 'title',
          type: 'text',
          defaultValue: 'Lexicon - Geopolitica',
        },
        {
          name: 'description',
          type: 'textarea',
          defaultValue: 'Browse our comprehensive lexicon of geopolitical terms and concepts.',
        },
      ],
    },
  ],
  hooks: {
    afterChange: [revalidateLexicon],
  },
}
