import type { Metadata } from 'next/types'

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import { Card } from '@/components/Card'

export const dynamic = 'force-static'
export const revalidate = 600

export default async function NodesPage() {
  const payload = await getPayload({ config: configPromise })

  const nodes = await payload.find({
    collection: 'nodes',
    depth: 1,
    limit: 12,
    overrideAccess: false,
    select: {
      title: true,
      slug: true,
      categories: true,
      meta: true,
    },
  })

  return (
    <div className="pt-24 pb-24">
      <div className="container mb-16">
        <div className="prose dark:prose-invert max-w-none text-center">
          <h1 className="mb-8 lg:mb-16">Nodes</h1>
        </div>
      </div>

      {nodes.docs.length > 0 ? (
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {nodes.docs.map((node) => (
              <Card
                key={node.id}
                title={node.title}
                href={`/nodes/${node.slug}`}
                relationTo="nodes"
                doc={node}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="container">No nodes found.</div>
      )}
    </div>
  )
}

export function generateMetadata(): Metadata {
  return {
    title: `Nodes`,
  }
}
