import { getCachedGlobal } from '@/utilities/getGlobals'
import { Home } from '@/payload-types'

export async function getHomeData(): Promise<Home> {
  try {
    const homeData = await getCachedGlobal('home')()
    return homeData
  } catch (error) {
    console.error('Error fetching home data:', error)
    return {
      id: 0,
      title: 'Welcome to Geopolitica',
      description: 'Default description when home global is not available.',
      meta: {
        title: 'Geopolitica',
        description: 'Default meta description when home global is not available.',
      }
    }
  }
}
