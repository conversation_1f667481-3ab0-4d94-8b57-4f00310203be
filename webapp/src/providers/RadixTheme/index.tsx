'use client'

import { Theme as RadixTheme } from '@radix-ui/themes'
import React from 'react'
import { useTheme } from '../Theme'

export const RadixThemeProvider: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  const { theme } = useTheme()

  return (
    <RadixTheme
      appearance={theme === 'dark' ? 'dark' : 'light'}
      accentColor="blue"
      grayColor="slate"
      radius="medium"
      scaling="100%"
      className="flex flex-col min-h-screen"
    >
      {children}
    </RadixTheme>
  )
}
