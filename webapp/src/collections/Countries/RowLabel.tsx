'use client'
import { Country } from '@/payload-types'
import { RowLabelProps, useRowLabel } from '@payloadcms/ui'

export const RowLabel: React.FC<RowLabelProps> = () => {
  const data = useRowLabel<NonNullable<Country['dropdowns']>[number]>()

  const label = data?.data?.title
    ? `Dropdown ${data.rowNumber !== undefined ? data.rowNumber + 1 : ''}: ${data?.data?.title}`
    : `Dropdown ${String((data.rowNumber ?? 0) + 1).padStart(2, '0')}`

  return <div>{label}</div>
}
