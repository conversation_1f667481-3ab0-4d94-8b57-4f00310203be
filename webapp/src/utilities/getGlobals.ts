import type { Config, Footer, Header, Home, Lexicon } from 'src/payload-types'

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { unstable_cache } from 'next/cache'

type Global = keyof Config['globals']

type GlobalTypeMap = {
  header: Header
  footer: Footer
  home: Home
  lexicon: Lexicon
}

// Generic function to get a global with proper typing
async function getGlobal<T extends Global>(slug: T, depth = 0): Promise<GlobalTypeMap[T]> {
  const payload = await getPayload({ config: configPromise })

  const global = await payload.findGlobal({
    slug,
    depth,
  })

  return global as GlobalTypeMap[T]
}

/**
 * Returns a unstable_cache function mapped with the cache tag for the slug
 */
export const getCachedGlobal = <T extends Global>(slug: T, depth = 0) =>
  unstable_cache(async () => getGlobal<T>(slug, depth), [slug], {
    tags: [`global_${slug}`],
  })
