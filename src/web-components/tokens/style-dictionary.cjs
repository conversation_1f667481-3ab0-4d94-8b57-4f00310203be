const StyleDictionary = require('style-dictionary');
const baseConfig = require('./config.json');

StyleDictionary.registerTransform({
  name: `custom/font`,
  ...require('./transformers/webFont.cjs'),
});

StyleDictionary.registerTransform({
  name: `custom/elevation`,
  ...require('./transformers/webElevation.cjs'),
});

StyleDictionary.registerTransform({
  name: `custom/border`,
  ...require('./transformers/webBorder.cjs'),
});

StyleDictionary.registerTransform({
  name: 'size/percent',
  type: 'value',
  matcher: token => {
    return token.unit === 'percent' && token.value !== 0;
  },
  transformer: token => {
    return `${token.value}%`;
  },
});

StyleDictionary.registerTransform({
  name: 'custom/state-layer',
  ...require('./transformers/webStateLayer.cjs'),
});

StyleDictionary.registerTransformGroup({
  name: 'custom/css',
  transforms: StyleDictionary.transformGroup['css'].concat([
    'size/px',
    'size/percent',
    'custom/elevation',
    'custom/font',
    'custom/border',
    'custom/state-layer',
  ]),
});

StyleDictionary.registerTransformGroup({
  name: 'custom/scss',
  transforms: StyleDictionary.transformGroup['scss'].concat([
    'size/px',
  ]),
});

StyleDictionary.registerFilter({
  name: 'validToken',
  matcher: require('./transformers/filterWeb.cjs'),
});

const StyleDictionaryExtended = StyleDictionary.extend(baseConfig);

StyleDictionaryExtended.buildAllPlatforms();
