export default {
  source: [
    "tokens/json/**/*.json"
  ],
  platforms: {
    css: {
      transformGroup: "custom/css",
      buildPath: "src/rootStyles/",
      files: [
        {
          destination: "design-tokens.css",
          format: "css/variables",
          filter: "validToken",
          options: {
            showFileHeader: false
          }
        }
      ]
    },
    scss: {
      transformGroup: "custom/scss",
      buildPath: "src/rootStyles/",
      files: [
        {
          destination: "design-tokens.scss",
          format: "scss/variables",
          filter: "validToken",
          options: {
            showFileHeader: false
          }
        }
      ]
    },
    json: {
      transformGroup: "custom/css",
      buildPath: "src/rootStyles/",
      files: [
        {
          destination: "design-tokens.json",
          format: "json"
        }
      ]
    },
    js: {
      transformGroup: "custom/css",
      buildPath: "public/",
      files: [
        {
          destination: "design-tokens.cjs",
          format: "javascript/module"
        }
      ]
    }
  }
}
