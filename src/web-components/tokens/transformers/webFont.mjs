import ensurePx from './ensurePx.mjs';

const notDefault = (value, defaultValue) => (value !== defaultValue) ? value : ''

const fontFamily = ({ fontFamily }, { fontFamilies } = {}) => fontFamilies && fontFamilies[fontFamily] ? fontFamilies[fontFamily] : fontFamily

export default {
  type: 'value',
  transitive: true,
  filter: function (token) {
    return token.type === 'custom-fontStyle'
  },
  transform: function ({ value: font }, options) {
    // font: font-style font-variant font-weight font-size/line-height font-family;
    return `${notDefault(font.fontStretch, 'normal')} ${notDefault(font.fontStyle, 'normal')} ${font.fontWeight} ${ensurePx(font.fontSize)}/${ensurePx(font.lineHeight)} ${fontFamily(font, options)}`.trim()
  }
}
