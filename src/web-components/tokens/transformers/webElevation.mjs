import { TinyColor } from '@ctrl/tinycolor';
import ensurePx from './ensurePx.mjs';

function transformElevation(elevation) {
  const { x, y, blur, spread, color, opacity } = elevation;
  const rgbColor = new TinyColor(color)
    .setAlpha(opacity)
    .toRgbString().toString();

  return `${ensurePx(x)} ${ensurePx(y)} ${ensurePx(blur)} ${ensurePx(spread)} ${rgbColor}`;
}

export default {
  type: 'value',
  transitive: true,
  filter: function (token) {
    return token.type === 'custom-elevation'
  },
  transform: function ({ value: elevations }, options) {
    // box-shadow: x y blur spread rgba(0, 0, 0, 0.15), x y blur spread rgba(0, 0, 0, 0.30);
    return Array.isArray(elevations) ? elevations.map(transformElevation).join(', ') : transformElevation(elevations);
  }
}
