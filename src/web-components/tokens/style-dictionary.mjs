import StyleDictionary from "style-dictionary";
import baseConfig from "./config.mjs";
import webFont from './transformers/webFont.mjs';
import webElevation from './transformers/webElevation.mjs';
import webBorder from './transformers/webBorder.mjs';
import webStateLayer from './transformers/webStateLayer.mjs';
import filterWeb from './transformers/filterWeb.mjs';

// Create the StyleDictionary instance with the config
const sd = new StyleDictionary(baseConfig);

// Register custom transforms
sd.registerTransform({
  name: `custom/font`,
  ...webFont,
});

sd.registerTransform({
  name: `custom/elevation`,
  ...webElevation,
});

sd.registerTransform({
  name: `custom/border`,
  ...webBorder,
});

sd.registerTransform({
  name: 'custom/state-layer',
  ...webStateLayer,
});

sd.registerTransform({
  name: 'size/percent',
  type: 'value',
  filter: token => {
    return token.unit === 'percent' && token.value !== 0;
  },
  transform: token => {
    return `${token.value}%`;
  },
});

// Register custom transform groups
// Use the built-in CSS transforms as base and add our custom ones
sd.registerTransformGroup({
  name: 'custom/css',
  transforms: sd.hooks.transformGroups['css'].concat([
    'size/px',
    'size/percent',
    'custom/elevation',
    'custom/font',
    'custom/border',
    'custom/state-layer',
  ]),
});

sd.registerTransformGroup({
  name: 'custom/scss',
  transforms: sd.hooks.transformGroups['scss'].concat([
    'size/px',
  ]),
});

// Register custom filter
sd.registerFilter({
  name: 'validToken',
  filter: filterWeb,
});

// Build all platforms
await sd.buildAllPlatforms();
