{"name": "@snf/design-system-components", "version": "0.65.0", "type": "module", "main": "dist/index.js", "web-types": "dist/web-types.json", "customElements": "dist/custom-elements.json", "exports": {".": "./dist/index.js", "./*": "./dist/*"}, "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite build --watch", "link": "npm link", "precheck": "npm run prebuild", "check": "tsc --noEmit --emitDeclarationOnly false", "lint": "eslint src/**/*.ts && stylelint src/**/*.{css,scss}", "lint:fix": "eslint src/**/*.ts --fix && stylelint src/**/*.{css,scss} --fix", "analyze": "lit-analyzer", "prebuild": "npm run design-tokens:generate", "build": "vite build", "build:link": "npm run build && npm run link", "storybook": "storybook dev -p 6006 --no-open", "prebuild-storybook": "npm run build", "build-storybook": "storybook build", "test": "vitest run --reporter=verbose --reporter=junit --coverage", "test:watch": "vitest", "design-tokens:generate": "node tokens/style-dictionary.mjs"}, "dependencies": {"@floating-ui/dom": "1.7.2", "@neodrag/vanilla": "2.3.1", "@tanstack/match-sorter-utils": "8.19.4", "@tanstack/table-core": "8.21.3", "csv": "6.4.0", "highcharts": "12.3.0", "lit": "3.3.1", "lit-html": "3.3.0"}, "devDependencies": {"@babel/core": "7.28.0", "@chromatic-com/storybook": "3.2.7", "@ctrl/tinycolor": "4.1.0", "@easepick/core": "1.2.1", "@easepick/kbd-plugin": "1.2.1", "@easepick/range-plugin": "1.2.1", "@lit-labs/virtualizer": "2.1.1", "@open-wc/testing-helpers": "3.0.1", "@storybook/addon-a11y": "8.6.14", "@storybook/addon-actions": "8.6.14", "@storybook/addon-essentials": "8.6.14", "@storybook/addon-links": "8.6.14", "@storybook/addon-mdx-gfm": "8.6.14", "@storybook/preview-api": "8.6.14", "@storybook/manager-api": "8.6.14", "@storybook/blocks": "8.6.14", "@storybook/theming": "8.6.14", "@storybook/web-components": "8.6.14", "@storybook/web-components-vite": "8.6.14", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/user-event": "14.6.1", "@types/postcss-import": "14.0.3", "@typescript-eslint/eslint-plugin": "8.20.0", "@vitest/coverage-v8": "2.1.8", "@web-types/lit": "2.0.0-3", "babel-loader": "10.0.0", "cem-plugin-expanded-types": "1.4.0", "custom-element-jet-brains-integration": "1.6.2", "date-fns": "4.1.0", "element-internals-polyfill": "3.0.2", "eslint": "9.18.0", "eslint-config-standard-with-typescript": "43.0.1", "eslint-plugin-import": "2.32.0", "eslint-plugin-n": "17.17.0", "eslint-plugin-promise": "7.2.1", "eslint-plugin-storybook": "0.12.0", "globby": "14.1.0", "jsdom": "26.1.0", "pascal-case": "4.0.0", "postcss": "8.5.6", "postcss-import": "16.1.0", "react": "19.1.0", "react-dom": "19.1.0", "sass": "1.89.2", "shadow-dom-testing-library": "1.12.0", "storybook": "8.6.14", "style-dictionary": "4.4.0", "stylelint": "16.21.1", "stylelint-config-sass-guidelines": "11.1.0", "ts-lit-plugin": "2.0.2", "typescript": "5.7.3", "vite": "7.0.0", "vite-plugin-cem": "0.8.3", "vite-plugin-dts": "4.5.4", "vite-plugin-svgo": "2.0.0", "vite-plugin-turbosnap": "1.0.3", "vitest": "2.1.8", "yup": "1.6.1"}}