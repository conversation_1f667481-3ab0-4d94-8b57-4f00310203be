@use "../colors/color" as color;
@use "../shapes/shape" as shape;
@use "../elevations/elevation" as elevation;

$card-theme-overrides: (
  outlined-container-color: color.color-scheme(surface),
);

$dialog-theme-overrides: (
  container-shape: shape.shape(none),
  container-elevation-shadow: elevation.elevation(2),
  container-max-width: 80vw,
  container-min-width: 280px,
  subhead-font: var(--typescale-title-large-font),
  subhead-line-height: var(--typescale-title-large-line-height),
  subhead-size: var(--typescale-title-large-size),
  subhead-weight: var(--typescale-title-large-weight),
  subhead-tracking: var(--typescale-title-large-tracking),
);

$table-theme-overrides: (
  header-headline-color: color.color-scheme(secondary),
  header-container-height: 32px,
  header-headline-weight: var(--typeface-weight-regular),
  row-item-outline-color: color.color-scheme(outline-variant),
);

$expansion-panel-theme-overrides: (
  container-background-color: color.color-scheme(on-primary),
  container-text-color: color.color-scheme(on-secondary-container),
  header-hover-state-layer-color: color.color-scheme(secondary-container),
  header-focus-state-layer-color: color.color-scheme(secondary-container),
  header-text-color: color.color-scheme(on-secondary-container),
  header-expanded-state-height: 70px,
  header-collapsed-state-height: 70px,
  container-text-size: var(--typescale-body-medium-size),
  container-text-line-height: var(--typescale-body-medium-line-height),
);

$menu-theme-overrides: (
  container-color: color.color-scheme(surface),
  container-shape: shape.shape(extra-small),
  container-elevation-shadow: elevation.elevation(5)
);

$form-field-theme-overrides: (
  outlined-container-shape: shape.shape(none),
);

$select-theme-overrides: (
  panel-background-color: color.color-scheme(surface),
  container-elevation-shadow: elevation.elevation(5)
);
