import React from "react";
import { <PERSON>I<PERSON>, ColorPalette, Meta } from "@storybook/blocks";
import tokens from "../design-tokens.json";
import { groupColors, getReferenceName } from "../color.helper";
import { ColorScheme } from "../../storybook/color-scheme";
import { ColorSchemeItem } from "../../storybook/color-scheme-item";
import { ColorSchemeItemGroup } from "../../storybook/color-scheme-item-group";

<Meta title="Design Tokens/Colors" />

# Colors

The SNF Design Systems uses color palettes according to Material Design 3. The color palette is defined in the design tokens and is used to define the color schemes.

## Color Schemes

Color schemes define the colors to be used in certain scenarios. They are derived from the color palette.

<div style={{ fontSize: '1.5em', backgroundColor: 'yellow', marginBottom: '20px' }}>For development please refer to the color schemes whenever it is possible, because the color schemes are defining our theme. Do not use colors from the palettes.</div>

### Material Design 3 Color Schemes

These color schemes where derived from a material design 3 color scheme and define a material design 3 light theme. We are using the default components of Angular Material ub our applications so we need to be as close as possible to material design 3 in our design system.

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gridTemplateRows: 'auto auto auto', gap: '20px' }}>
  <div style={{ marginBottom: '10px' }}>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["primary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["primary"].original.value)} contrastColor={tokens.color.scheme["on-primary"].value} color={tokens.color.scheme["primary"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-primary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-primary"].original.value)} contrastColor={tokens.color.scheme["primary"].value} color={tokens.color.scheme["on-primary"].value} />

      <ColorSchemeItem name={tokens.color.scheme["primary-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["primary-container"].original.value)} contrastColor={tokens.color.scheme["on-primary-container"].value} color={tokens.color.scheme["primary-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-primary-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-primary-container"].original.value)} contrastColor={tokens.color.scheme["primary-container"].value} color={tokens.color.scheme["on-primary-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["secondary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["secondary"].original.value)} contrastColor={tokens.color.scheme["on-secondary"].value} color={tokens.color.scheme["secondary"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-secondary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-secondary"].original.value)} contrastColor={tokens.color.scheme["secondary"].value} color={tokens.color.scheme["on-secondary"].value} />

      <ColorSchemeItem name={tokens.color.scheme["secondary-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["secondary-container"].original.value)} contrastColor={tokens.color.scheme["on-secondary-container"].value} color={tokens.color.scheme["secondary-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-secondary-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-secondary-container"].original.value)} contrastColor={tokens.color.scheme["secondary-container"].value} color={tokens.color.scheme["on-secondary-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["tertiary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["tertiary"].original.value)} contrastColor={tokens.color.scheme["on-tertiary"].value} color={tokens.color.scheme["tertiary"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-tertiary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-tertiary"].original.value)} contrastColor={tokens.color.scheme["tertiary"].value} color={tokens.color.scheme["on-tertiary"].value} />

      <ColorSchemeItem name={tokens.color.scheme["tertiary-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["tertiary-container"].original.value)} contrastColor={tokens.color.scheme["on-tertiary-container"].value} color={tokens.color.scheme["tertiary-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-tertiary-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-tertiary-container"].original.value)} contrastColor={tokens.color.scheme["tertiary-container"].value} color={tokens.color.scheme["on-tertiary-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["error"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["error"].original.value)} contrastColor={tokens.color.scheme["on-error"].value} color={tokens.color.scheme["error"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-error"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-error"].original.value)} contrastColor={tokens.color.scheme["error"].value} color={tokens.color.scheme["on-error"].value} />

      <ColorSchemeItem name={tokens.color.scheme["error-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["error-container"].original.value)} contrastColor={tokens.color.scheme["on-error-container"].value} color={tokens.color.scheme["error-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-error-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-error-container"].original.value)} contrastColor={tokens.color.scheme["error-container"].value} color={tokens.color.scheme["on-error-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["primary-fixed"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["primary-fixed"].original.value)} contrastColor={tokens.color.scheme["on-primary-fixed"].value} color={tokens.color.scheme["primary-fixed"].value} />

        <ColorSchemeItem name={tokens.color.scheme["primary-fixed-dim"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["primary-fixed-dim"].original.value)} contrastColor={tokens.color.scheme["on-primary-fixed"].value} color={tokens.color.scheme["primary-fixed-dim"].value} />
      </ColorSchemeItemGroup>

      <ColorSchemeItem name={tokens.color.scheme["on-primary-fixed"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-primary-fixed"].original.value)} contrastColor={tokens.color.scheme["primary-fixed"].value} color={tokens.color.scheme["on-primary-fixed"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-primary-fixed-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-primary-fixed-variant"].original.value)} contrastColor={tokens.color.scheme["primary-fixed"].value} color={tokens.color.scheme["on-primary-fixed-variant"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["secondary-fixed"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["secondary-fixed"].original.value)} contrastColor={tokens.color.scheme["on-secondary-fixed"].value} color={tokens.color.scheme["secondary-fixed"].value} />

        <ColorSchemeItem name={tokens.color.scheme["secondary-fixed-dim"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["secondary-fixed-dim"].original.value)} contrastColor={tokens.color.scheme["on-secondary-fixed"].value} color={tokens.color.scheme["secondary-fixed-dim"].value} />
      </ColorSchemeItemGroup>

      <ColorSchemeItem name={tokens.color.scheme["on-secondary-fixed"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-secondary-fixed"].original.value)} contrastColor={tokens.color.scheme["secondary-fixed"].value} color={tokens.color.scheme["on-secondary-fixed"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-secondary-fixed-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-secondary-fixed-variant"].original.value)} contrastColor={tokens.color.scheme["secondary-fixed"].value} color={tokens.color.scheme["on-secondary-fixed-variant"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["tertiary-fixed"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["tertiary-fixed"].original.value)} contrastColor={tokens.color.scheme["on-tertiary-fixed"].value} color={tokens.color.scheme["tertiary-fixed"].value} />

        <ColorSchemeItem name={tokens.color.scheme["tertiary-fixed-dim"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["tertiary-fixed-dim"].original.value)} contrastColor={tokens.color.scheme["on-tertiary-fixed"].value} color={tokens.color.scheme["tertiary-fixed-dim"].value} />
      </ColorSchemeItemGroup>

      <ColorSchemeItem name={tokens.color.scheme["on-tertiary-fixed"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-tertiary-fixed"].original.value)} contrastColor={tokens.color.scheme["tertiary-fixed"].value} color={tokens.color.scheme["on-tertiary-fixed"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-tertiary-fixed-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-tertiary-fixed-variant"].original.value)} contrastColor={tokens.color.scheme["tertiary-fixed"].value} color={tokens.color.scheme["on-tertiary-fixed-variant"].value} />
    </ColorScheme>

  </div>

  <div style={{ gridColumn: 'span 3' }}>
    <ColorScheme>
      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["surface-dim"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-dim"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-dim"].value} />

        <ColorSchemeItem name={tokens.color.scheme["surface"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface"].value} />

        <ColorSchemeItem name={tokens.color.scheme["surface-bright"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-bright"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-bright"].value} />
      </ColorSchemeItemGroup>

      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["surface-container-lowest"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-container-lowest"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-container-lowest"].value} />

        <ColorSchemeItem name={tokens.color.scheme["surface-container-low"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-container-low"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-container-low"].value} />

        <ColorSchemeItem name={tokens.color.scheme["surface-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-container"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-container"].value} />

        <ColorSchemeItem name={tokens.color.scheme["surface-container-high"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-container-high"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-container-high"].value} />

        <ColorSchemeItem name={tokens.color.scheme["surface-container-highest"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["surface-container-highest"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["surface-container-highest"].value} />
      </ColorSchemeItemGroup>

      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["on-surface"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-surface"].original.value)} contrastColor={tokens.color.scheme["surface"].value} color={tokens.color.scheme["on-surface"].value} />

        <ColorSchemeItem name={tokens.color.scheme["on-surface-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-surface-variant"].original.value)} contrastColor={tokens.color.scheme["surface"].value} color={tokens.color.scheme["on-surface-variant"].value} />

        <ColorSchemeItem name={tokens.color.scheme["outline"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["outline"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["outline"].value} />

        <ColorSchemeItem name={tokens.color.scheme["outline-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["outline-variant"].original.value)} contrastColor={tokens.color.scheme["on-surface"].value} color={tokens.color.scheme["outline-variant"].value} />
      </ColorSchemeItemGroup>
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["inverse-surface"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["inverse-surface"].original.value)} contrastColor={tokens.color.scheme["inverse-on-surface"].value} color={tokens.color.scheme["inverse-surface"].value} />

      <ColorSchemeItem name={tokens.color.scheme["inverse-on-surface"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["inverse-on-surface"].original.value)} contrastColor={tokens.color.scheme["inverse-surface"].value} color={tokens.color.scheme["inverse-on-surface"].value} />

      <ColorSchemeItem name={tokens.color.scheme["inverse-primary"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["inverse-primary"].original.value)} contrastColor={tokens.color.scheme["primary"].value} color={tokens.color.scheme["inverse-primary"].value} />

      <ColorSchemeItemGroup>
        <ColorSchemeItem name={tokens.color.scheme["scrim"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["scrim"].original.value)} contrastColor={tokens.color.scheme["surface"].value} color={tokens.color.scheme["scrim"].value} />

        <ColorSchemeItem name={tokens.color.scheme["shadow"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["shadow"].original.value)} contrastColor={tokens.color.scheme["surface"].value} color={tokens.color.scheme["shadow"].value} />
      </ColorSchemeItemGroup>
    </ColorScheme>

  </div>
</div>

### Additional Color Schemes

We need some additional color schemes for indicating different information categories such as success, warnings etc.

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gridTemplateRows: 'auto auto auto', gap: '20px' }}>
  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["success"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["success"].original.value)} contrastColor={tokens.color.scheme["on-success"].value} color={tokens.color.scheme["success"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-success"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-success"].original.value)} contrastColor={tokens.color.scheme["success"].value} color={tokens.color.scheme["on-success"].value} />

      <ColorSchemeItem name={tokens.color.scheme["success-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["success-container"].original.value)} contrastColor={tokens.color.scheme["on-success-container"].value} color={tokens.color.scheme["success-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-success-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-success-container"].original.value)} contrastColor={tokens.color.scheme["success-container"].value} color={tokens.color.scheme["on-success-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["warning"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["warning"].original.value)} contrastColor={tokens.color.scheme["on-warning"].value} color={tokens.color.scheme["warning"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-warning"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-warning"].original.value)} contrastColor={tokens.color.scheme["warning"].value} color={tokens.color.scheme["on-warning"].value} />

      <ColorSchemeItem name={tokens.color.scheme["warning-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["warning-container"].original.value)} contrastColor={tokens.color.scheme["on-warning-container"].value} color={tokens.color.scheme["warning-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-warning-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-warning-container"].original.value)} contrastColor={tokens.color.scheme["warning-container"].value} color={tokens.color.scheme["on-warning-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["info"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["info"].original.value)} contrastColor={tokens.color.scheme["on-info"].value} color={tokens.color.scheme["info"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-info"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-info"].original.value)} contrastColor={tokens.color.scheme["info"].value} color={tokens.color.scheme["on-info"].value} />

      <ColorSchemeItem name={tokens.color.scheme["info-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["info-container"].original.value)} contrastColor={tokens.color.scheme["on-info-container"].value} color={tokens.color.scheme["info-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-info-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-info-container"].original.value)} contrastColor={tokens.color.scheme["info-container"].value} color={tokens.color.scheme["on-info-container"].value} />
    </ColorScheme>

  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["purple"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["purple"].original.value)} contrastColor={tokens.color.scheme["on-purple"].value} color={tokens.color.scheme["purple"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-purple"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-purple"].original.value)} contrastColor={tokens.color.scheme["purple"].value} color={tokens.color.scheme["on-purple"].value} />

      <ColorSchemeItem name={tokens.color.scheme["purple-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["purple-container"].original.value)} contrastColor={tokens.color.scheme["on-purple-container"].value} color={tokens.color.scheme["purple-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-purple-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-purple-container"].original.value)} contrastColor={tokens.color.scheme["purple-container"].value} color={tokens.color.scheme["on-purple-container"].value} />
    </ColorScheme>

  </div>
  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["grey"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["grey"].original.value)} contrastColor={tokens.color.scheme["on-grey"].value} color={tokens.color.scheme["grey"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-grey"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-grey"].original.value)} contrastColor={tokens.color.scheme["grey"].value} color={tokens.color.scheme["on-grey"].value} />

      <ColorSchemeItem name={tokens.color.scheme["grey-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["grey-container"].original.value)} contrastColor={tokens.color.scheme["on-grey-container"].value} color={tokens.color.scheme["grey-container"].value} />

      <ColorSchemeItem name={tokens.color.scheme["on-grey-container"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-grey-container"].original.value)} contrastColor={tokens.color.scheme["grey-container"].value} color={tokens.color.scheme["on-grey-container"].value} />
    </ColorScheme>
  </div>

  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["white"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["white"].original.value)} contrastColor={tokens.color.scheme["primary"].attributes.item} color={tokens.color.scheme["white"].value} />
      <ColorSchemeItem name={tokens.color.scheme["black"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["black"].original.value)} contrastColor={tokens.color.scheme["white"].attributes.item} color={tokens.color.scheme["black"].value} />
    </ColorScheme>
  </div>
  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["font"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["font"].original.value)} contrastColor={tokens.color.scheme["on-font"].value} color={tokens.color.scheme["font"].value} />
      <ColorSchemeItem name={tokens.color.scheme["on-font"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-font"].original.value)} contrastColor={tokens.color.scheme["font"].value} color={tokens.color.scheme["on-font"].value} />
    </ColorScheme>
  </div>
  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["font-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["font-variant"].original.value)} contrastColor={tokens.color.scheme["on-font-variant"].value} color={tokens.color.scheme["font-variant"].value} />
      <ColorSchemeItem name={tokens.color.scheme["on-font-variant"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-font-variant"].original.value)} contrastColor={tokens.color.scheme["font-variant"].value} color={tokens.color.scheme["on-font-variant"].value} />
    </ColorScheme>
  </div>
  <div>
    <ColorScheme>
      <ColorSchemeItem name={tokens.color.scheme["link"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["link"].original.value)} contrastColor={tokens.color.scheme["on-link"].value} color={tokens.color.scheme["link"].value} />
      <ColorSchemeItem name={tokens.color.scheme["on-link"].attributes.item} colorRef={getReferenceName(tokens.color.scheme["on-link"].original.value)} contrastColor={tokens.color.scheme["link"].value} color={tokens.color.scheme["on-link"].value} />
    </ColorScheme>
  </div>
</div>

## Color Palettes

These color palettes show the base colors of the snf design system. In components, don't use these colors directly but prefer using the
abstract colors below. This allows for later color changes/theming/dark mode without having to rename a lot of
properties.

<ColorPalette>
  {Object.entries(groupColors(tokens.color.palette)).map(
    ([name, swatches], index) => (
      <ColorItem key={index} title={name} subtitle={""} colors={swatches} />
    )
  )}
</ColorPalette>

### SCSS

Check the documentation on the top of the page to see if the color is in the palette or in the schemes.

```scss
@use "@snf/design-system-components/style.scss" as snf-library;

.primary-text {
  color: snf-library.color-scheme("primary");
}

.error-text {
  color: snf-library.color-scheme("on-error");
}

.success-text {
  color: snf-library.color-scheme("on-background");
}

// ONLY IF NOT OTHER POSSIBLE
.success-text {
  color: snf-library.color-palette("primary-10");
}
```

### Angular Material

Documentation: https://material.angular.io/guide/theming-your-components#reading-tonal-palette-colors

#### Theme Usage

```scss
@use "@angular/material" as mat;

// Get primary color from theme
.my-element {
  color: mat.get-theme-color($my-theme, primary);
  background: mat.get-theme-color($my-theme, on-secondary);
}

// Get a specific color from the theme palette
.my-element {
  color: mat.get-theme-color($my-theme, primary, 10);
}
```

### Default Color Usage (Used in all SCS)

```scss
@use "@snf/design-system-components/style" as snf;

```

### Configuration for Angular Material 3

In the version 3 of Angular Material, there are currently two ways to configure the colors for the theme.

1. Providing a color palette in a theme and applying it ([docs](https://material.angular.io/guide/theming#custom-theme))
2. Using the sys variables api to override the definition of the color roles (no docs at the moment, [issue link](https://github.com/angular/components/issues/29107#issuecomment-2147805584))

We need to use the second approach because we have defined ourselves the color specs in the design system, and the first approach would set automatically which color to take from the palette for which role.

**Note**: This is a way that Angular Material suggest and not a workaround

```scss
@use "@angular/material" as mat;
@use "@snf/design-system-components/style" as snf;
@use "theme" as theme;

@include mat.core();

// this compute the sys variables for angular material design 3 from our design tokens
$overriden-sys-variables: snf.override-sys-variables(theme.$snf-theme, (color));

html {
  @include mat.core-theme(theme.$snf-theme);
  @include mat.all-component-themes(theme.$snf-theme);
  // this generates the sys variables from the theme and overrides them with our design tokens
  @include mat.system-level-colors(theme.$snf-theme, $overriden-sys-variables);
  @include snf.all-responsive-styles();
}
```

#### Override Angular Material Dialog

```scss
// Override Theme Configuration for Angular Material Dialog
@use '@angular/material' as mat;
@use '@snf/design-system-components/style' as snf;

@mixin override() {
  * {
    @include mat.dialog-overrides((
      container-elevation-shadow: var(--elevation-2)
    ));
  }
}

@mixin theme($theme) {
  @if mat.theme-has($theme, color) {
    @include mat.dialog-color($theme);
    @include override();
  }
}

// Global Style File
@use '@angular/material' as mat;
@use '@snf/design-system-components/style' as snf;
@use 'theme' as theme;
@use "dialog-theme" as snf-dialog-theme;

html  {
  // NEW
  @include mat.core-theme(theme.$snf-theme);
  @include mat.all-component-themes(theme.$snf-theme);
  @include snf-dialog-theme.theme(theme.$snf-theme);
  @include snf.all-responsive-styles();
}
```
