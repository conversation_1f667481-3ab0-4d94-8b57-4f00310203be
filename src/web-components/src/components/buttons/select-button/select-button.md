## Usage

The Select Button component provides a button-like element that can be used to trigger dropdowns or selection menus. It has a consistent appearance with an arrow icon indicating it will open a selection.

### Basic Usage:

```html
<snf-select-button>
  <span>Select an option</span>
</snf-select-button>
```

### Disabled State:

```html
<snf-select-button disabled="true">
  <span>Can't select option</span>
</snf-select-button>
```

### Event Handling:

```javascript
const selectButton = document.querySelector('snf-select-button');
selectButton.addEventListener('select-click', () => {
  // Toggle your dropdown or selection menu
  selectButton.open = !selectButton.open;
  // Show your dropdown component
});
```

### Slots

- Default slot for the text to be displayed

```html
<snf-select-button>
  <span>Select an option</span>
</snf-select-button>
```

### Attributes

- `disabled` attribute to define whether the button is disabled (default: false)

```html
<snf-select-button disabled="true">
  <span>Can't select option</span>
</snf-select-button>
```

- `open` attribute to indicate whether the select dropdown is open (default: false)

```html
<snf-select-button open="true">
  <span>Currently selecting</span>
</snf-select-button>
```
