import { html, unsafeCSS } from "lit";
import {
  property,
} from "lit/decorators.js";
import BaseElement from "../../../internals/baseElement/baseElement";
import styles from "./select-button.scss?inline";
import { booleanAttributeConverter } from "../../../internals/utils/boolean-attribute-converter";
import "../../icon/icon.component";
import registerElement from "../../registerElement";

/**
 * @slot - Pass the content which should be displayed
 * @disabled Whether the button should be disabled (default: false)
 */
export default class SelectButton extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  public disabled: boolean = false;

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  public open: boolean = false;

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  public fullWidth: boolean = false;

  private handleSlotChange() {
    this.requestUpdate();
  }

  private handleClick() {
    this.dispatchEvent(new CustomEvent('selectClick', {
      bubbles: true,
      composed: true
    }));
  }

  render() {
    return html`<button
      class="container ${this.disabled ? "disabled" : ""} ${this.fullWidth ? "full-width" : ""}"
      ?disabled=${this.disabled}
      @click="${this.handleClick}"
    >
      <div class="text-container">
        <slot @slotchange=${this.handleSlotChange}></slot>
      </div>
      <snf-icon class="arrow-icon" iconStyle="filled" iconSize="small">${this.open ? 'arrow_drop_up' : 'arrow_drop_down'}</snf-icon>
    </button>`;
  }
}

registerElement("snf-select-button", SelectButton);

declare global {
  interface HTMLElementTagNameMap {
    "snf-select-button": SelectButton;
  }
}
