@use "@styles/style" as snf;

:host {
  display: block;

  .container {
    text-decoration: none;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    @include snf.label-large();
    color: snf.color-scheme(on-surface-variant);
    background-color: transparent;
    border: 1px solid snf.color-scheme(outline);
    border-radius: snf.shape(full);
    padding: snf.spacing(1) snf.spacing(3);
    transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
    overflow: hidden;

    &.full-width {
      width: 100%;
    }

    &:not(.disabled) {
      &:hover,
      &:active  {
        background-color: snf.hex-opacity(snf.$color-scheme-on-surface-variant, snf.$state-layer-hover);
      }

      &:focus,
      &:focus-visible {
        outline: snf.stroke(medium) solid snf.color-scheme(primary);
        outline-offset: -1px;
        background-color: snf.hex-opacity(snf.$color-scheme-on-surface-variant, snf.$state-layer-hover);
      }
    }

    &.disabled {
      pointer-events: none;
      cursor: unset;
      border-color: snf.color-scheme(inverse-on-surface);
      color: snf.hex-opacity(snf.$color-scheme-on-surface, snf.$state-layer-disable);
    }

    .arrow-icon {
      color: snf.color-scheme(primary);
      flex: 0 0 auto;
      margin-left: auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .text-container {
      flex: 1 1 auto;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
    }

    slot {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
