import IconButtonComponent from './icon-button.component';
import docs from './icon-button.md?raw';
import { html } from 'lit-html';
import { Meta, StoryFn, StoryObj } from '@storybook/web-components';
import { iconSizes, iconStyles } from '../../icon/icon.component';
import '../../icon/icon.component';
import './icon-button.component';

const meta: Meta<IconButtonComponent> = {
  title: 'Components/IconButton',
  component: 'snf-icon-button',
  argTypes: {
    iconStyle: { control: 'select', options: iconStyles },
    iconSize: { control: 'select', options: iconSizes },
    disabled: { control: 'boolean' },
  },
  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
  },
};
export default meta;

const Template: StoryFn<IconButtonComponent> = ({
  iconStyle,
  iconSize,
  disabled,
  slot,
}) => html`
  <snf-icon-button
    iconStyle=${iconStyle}
    iconSize=${iconSize}
    ?disabled=${disabled}
  >
    ${slot}
  </snf-icon-button>
`;

export const Default: StoryObj<IconButtonComponent> = {
  render: Template,
  args: {
    slot: 'home',
  },
};

export const Disabled: StoryObj<IconButtonComponent> = {
  render: Template,
  args: {
    slot: 'home',
    disabled: true,
  },
};

export const Outlined: StoryObj<IconButtonComponent> = {
  render: Template,
  args: {
    slot: 'home',
    iconStyle: 'outlined',
  },
};

export const Round: StoryObj<IconButtonComponent> = {
  render: Template,
  args: {
    slot: 'home',
    iconStyle: 'round',
  },
};

export const Medium: StoryObj<IconButtonComponent> = {
  render: Template,
  args: {
    slot: 'home',
    iconSize: 'medium'
  },
};

export const Large: StoryObj<IconButtonComponent> = {
  render: Template,
  args: {
    slot: 'home',
    iconSize: 'large'
  },
};

