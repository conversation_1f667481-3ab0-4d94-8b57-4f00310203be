import './icon-button.component';
import '../../icon/icon.component'
import { describe, expect, test, vi } from 'vitest';
import { elementUpdated, fixture, html } from '@open-wc/testing-helpers';

describe('IconButtonComponent', () => {
  test('should render the icon button with default properties', async () => {
    const element = await fixture(html`<snf-icon-button>home</snf-icon-button>`);
    await elementUpdated(element);

    const button = element.shadowRoot?.querySelector('button');
    const icon = button?.querySelector('snf-icon');

    expect(button).toBeTruthy();
    expect(icon).toBeTruthy();
    expect(icon?.getAttribute('iconStyle')).toBe('filled');
    expect(icon?.getAttribute('iconSize')).toBe('small');
  });

  test('should apply the disabled attribute to the button', async () => {
    const element = await fixture(html`<snf-icon-button disabled>home</snf-icon-button>`);
    await elementUpdated(element);

    const button = element.shadowRoot?.querySelector('button');
    expect(button?.hasAttribute('disabled')).toBe(true);
    expect(button?.getAttribute('aria-disabled')).toBe('true');
  });

  test('should update iconStyle and iconSize properties', async () => {
    const element = await fixture(html`<snf-icon-button iconStyle="outlined" iconSize="large">home</snf-icon-button>`);
    await elementUpdated(element);

    const icon = element.shadowRoot?.querySelector('snf-icon');
    expect(icon?.getAttribute('iconStyle')).toBe('outlined');
    expect(icon?.getAttribute('iconSize')).toBe('large');
  });

  test('should handle click events when not disabled', async () => {
    const element = await fixture(html`<snf-icon-button>home</snf-icon-button>`);
    const button = element.shadowRoot?.querySelector('button');
    const clickSpy = vi.fn();

    button?.addEventListener('click', clickSpy);
    button?.click();

    expect(clickSpy).toHaveBeenCalledOnce();
  });

  test('should not handle click events when disabled', async () => {
    const element = await fixture(html`<snf-icon-button disabled>home</snf-icon-button>`);
    const button = element.shadowRoot?.querySelector('button');
    const clickSpy = vi.fn();

    button?.addEventListener('click', clickSpy);
    button?.click();

    expect(clickSpy).not.toHaveBeenCalled();
  });
});
