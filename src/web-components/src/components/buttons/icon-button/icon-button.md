## Usage

The `icon-button` component wraps the `snf-icon` inside a button element, providing a clickable interface.

```html
<snf-icon-button>home</snf-icon-button>
```

## Properties

- `iconStyle`: Specifies the material icon font style (`filled`, `outlined`, `round`). Default is `filled`.
- `iconSize`: Specifies the icon size (`small`, `medium`, `large`). Default is `small`.
- `disabled`: Disables the button when set to `true`.

## Examples

### Default Icon Button

```html
<snf-icon-button>home</snf-icon-button>
```

### Disabled Icon Button

```html
<snf-icon-button disabled>home</snf-icon-button>
```
