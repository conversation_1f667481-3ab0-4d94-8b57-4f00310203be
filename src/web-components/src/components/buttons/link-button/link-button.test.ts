import "./link-button.component";
import "../../icon/icon.component";
import { describe, expect, test, vi } from "vitest";
import { elementUpdated, fixture, html } from "@open-wc/testing-helpers";

describe("LinkButton", () => {
  test("should display nothing when no icon or slot given", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button></snf-link-button> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(0);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=prefix]",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true }).length,
    ).toBe(0);

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=suffix]",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true }).length,
    ).toBe(0);
  });

  test("should display the default slot content when span element is provided", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button><span>some text</span></snf-link-button> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(1);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true })[0].outerHTML,
    ).toEqual("<span>some text</span>");
  });

  test("should display the prefix slot content as icon", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html`
        <snf-link-button>
          <snf-icon slot="prefix" iconStyle="filled" iconSize="medium"
            >add</snf-icon
          >
        </snf-link-button>
      `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=prefix]",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(1);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(2);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=prefix]",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true })[0].outerHTML,
    ).toEqual(
      '<snf-icon slot="prefix" iconstyle="filled" iconsize="medium" style="--icon-height: var(--icons-medium);">add</snf-icon>',
    );
  });

  test("should display the suffix slot content", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html`
        <snf-link-button>
          <snf-icon slot="suffix" iconStyle="filled" iconSize="medium"
            >add</snf-icon
          >
        </snf-link-button>
      `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=suffix]",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(1);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(2);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=suffix]",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true })[0].outerHTML,
    ).toEqual(
      '<snf-icon slot="suffix" iconstyle="filled" iconsize="medium" style="--icon-height: var(--icons-medium);">add</snf-icon>',
    );
  });

  test("should display the suffix and preffix slot content", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html`
        <snf-link-button>
          <snf-icon slot="prefix" iconStyle="filled" iconSize="medium"
            >add</snf-icon
          >
          <snf-icon slot="suffix" iconStyle="filled" iconSize="medium"
            >add</snf-icon
          >
        </snf-link-button>
      `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=suffix]",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(1);

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=prefix]",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(1);

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(3);
  });

  test("should display the text and prefix slot content if both are given", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button>
        <snf-icon slot="prefix" iconStyle="filled">add</snf-icon
        ><span>some text</span></snf-link-button
      >`,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=prefix]",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(1);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedNodes({ flatten: true }).length,
    ).toBe(2);
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot:not([name])",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true })[0].outerHTML,
    ).toEqual("<span>some text</span>");
    expect(
      (
        element.shadowRoot?.querySelector(
          "slot[name=prefix]",
        ) as HTMLSlotElement | null
      )?.assignedElements({ flatten: true })[0].outerHTML,
    ).toEqual(
      '<snf-icon slot="prefix" iconstyle="filled" iconsize="small" style="--icon-height: var(--icons-small);">add</snf-icon>',
    );
  });

  test("should add underlined class when underlined property is set to true", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button ?underlined=${true}></snf-link-button>`,
    );

    await elementUpdated(element);

    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).toContain("underlined");
  });

  test("should remove underlined class when underlined property is changed from true to false", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button ?underlined=${true}></snf-link-button>`,
    );
    await elementUpdated(element);

    // Initially, the underlined class should be present
    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).toContain("underlined");

    // Change the underlined property to false
    element.underlined = false;
    await elementUpdated(element);

    // Now, the underlined class should not be present
    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).not.toContain("underlined");
  });

  test("should add disabled class when disabled property is set to true", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button ?disabled=${true}></snf-link-button>`,
    );

    await elementUpdated(element);

    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).toContain("disabled");
  });

  test("should remove disabled class when disabled property is changed from true to false", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button ?disabled=${true}></snf-link-button>`,
    );
    await elementUpdated(element);

    // Initially, the disabled class should be present
    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).toContain("disabled");

    // Change the disabled property to false
    element.disabled = false;
    await elementUpdated(element);

    // Now, the disabled class should not be present
    expect(
      element.shadowRoot?.querySelector(".container")?.className,
    ).not.toContain("disabled");
  });

  test("should set role link to the anchor when href is provided", async () => {
    const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
      html` <snf-link-button href="https://www.google.com"></snf-link-button>`,
    );

    await elementUpdated(element);

    expect(element.shadowRoot?.querySelector("a")?.getAttribute("role")).toBe(
      "link",
    );
  });

  describe('button', () => {
    test("should set button if no link is provided", async () => {
      const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
        html` <snf-link-button></snf-link-button>`,
      );

      await elementUpdated(element);

      expect(element.shadowRoot?.querySelector("button")).toBeTruthy();
    });
  });

  describe("target", () => {
    test("should open a new tab when the target is _blank, href is provided, then the link has target blank", async () => {
      const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
        html` <snf-link-button
          href="https://www.google.com"
          target="_blank"
        ></snf-link-button>`,
      );

      await elementUpdated(element);

      expect(element.shadowRoot?.querySelector("a")?.target).toBe("_blank");
    });

    test("should show the open_in_new icon when the target is _blank and remove the suffix slot", async () => {
      const element: HTMLElementTagNameMap["snf-link-button"] = await fixture(
        html` <snf-link-button
          href="https://www.google.com"
          target="_blank"
        ></snf-link-button>`,
      );

      await elementUpdated(element);

      expect(
        (
          element.shadowRoot?.querySelector(
            "slot[name=suffix]",
          ) as HTMLSlotElement | null
        )?.assignedElements({ flatten: true }).length ?? 0,
      ).toBe(0);

      expect(
        (element.shadowRoot?.querySelector(".icon") as HTMLElement | null)
          ?.textContent,
      ).toBe("open_in_new");
    });
  });
});
