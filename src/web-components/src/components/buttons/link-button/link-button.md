## Usage

There are 3 use cases for this component:

- The component is not a link but a **button**: nothing has to be done, no href is provided, role is automatically button and the developer can handle the event propagation in angular

- The component is a **link** with an **internal URL**: Role is link, the developer can use the custom **angular directives**

- The component is a **link** with an **external URL**: Role is link, the developer just passes the URL in the **href** property

### Button:

```html
<snf-link-button>
  <span>my normal button</span>
</snf-link-button>
```

### Link with external URL

```html
<snf-link-button href="target-location">
  <span>my link button</span>
</snf-link-button>
```

When used as a link, it behaves as a normal html anchor, so it also supports all the built-in funtionalities an html anchot has.

### Slots

- General text slot for the text to be displayed

```html
<snf-link-button>
  <span>that's my text</span>
</snf-link-button>
```

- or just as text node

```html
<snf-link-button>value as text node</snf-link-button>
```

- Prefix slot for the icon to be displayed in front of the text

```html
<snf-link-button>
  <snf-icon slot="prefix" iconStyle="filled" iconSize="small">add</snf-icon>
  <span>button text</span>
</snf-link-button>
```

- `underlined` attribute to define whether the text is underlined or not (default: true)

```html
<snf-link-button underlined="true">
  <span>button text</span>
</snf-link-button>
```

- `disabled` attribute to define whether the button is disabled or not (default: false, so enabled)

```html
<snf-link-button disabled="true">
  <span>button text</span>
</snf-link-button>
```

## Navigation

[See the custom directves documentation](?path=/docs/services-navigation--docs#-angular-routing-directives)

Use those directives for navigating using the **angular router**.

```html
<snf-link-button [gmSnfRouterLink]="/calls/">
  <span>my normal button</span>
</snf-link-button>
```

When nagigating to an external link, just use the href attribute like a normal anchor

```html
<snf-link-button href="hwww.google.com">
  <span>my normal button</span>
</snf-link-button>
```

The target attribute can also be provided to configure the navigation

```html
<snf-link-button href="hwww.google.com" target="_blank">
  <span>my normal button</span>
</snf-link-button>
```

**Note**: When target is `_blank`, the `open_in_new` icon is shown as suffix.
