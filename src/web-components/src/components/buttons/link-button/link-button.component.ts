import { html, nothing, unsafeCSS } from "lit";
import {
  property,
  queryAssignedElements,
} from "lit/decorators.js";
import BaseElement from "../../../internals/baseElement/baseElement";
import styles from "./link-button.scss?inline";
import { booleanAttributeConverter } from "../../../internals/utils/boolean-attribute-converter";
import "../../icon/icon.component";
import registerElement from "../../registerElement";

export const linkButtonTargets = [
  "_blank",
  "_self",
  "_parent",
  "_top",
] as const;
export type LinkButtonTargets = (typeof linkButtonTargets)[number];
/**
 * @slot - Pass the text which should be displayed
 * @slot prefix - Pass the `snf-icon` (or whatever you need) element to add an icon which should be displayed before the text (optional)
 * @slot suffix - Pass the `snf-icon` (or whatever you need) element to add an icon which should be displayed after the text (optional)
 * @href - Add a link to the button which should be opened when the button is clicked. (default: "#")
 * @underlined Whether the text should be underlined (default: true)
 * @disabled Whether the button should be disabled (default: false)
 */
export default class LinkButton extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({ type: String, reflect: true })
  public href?: string;

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  public underlined: boolean = true;

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  public disabled: boolean = false;

  @property({ type: String, reflect: true })
  public target: LinkButtonTargets | undefined;

  @queryAssignedElements({ slot: "prefix" }) prefixSlot!: Array<HTMLElement>;
  @queryAssignedElements({ slot: "suffix" }) suffixSlot!: Array<HTMLElement>;

  private get hasPrefixSlot() {
    return this.prefixSlot.length > 0;
  }

  private get hasSuffixSlot() {
    return this.suffixSlot.length > 0;
  }

  private handleSlotChange() {
    this.requestUpdate();
  }

  private get isLink() {
    return !!this.href && this.href !== "";
  }

  private get hasBlankTarget() {
    return this.target === "_blank";
  }

  private get suffix() {
    return this.hasBlankTarget
      ? html`<snf-icon class="icon" iconstyle="filled" iconsize="small"
          >open_in_new</snf-icon
        >`
      : html`<slot name="suffix" @slotchange=${this.handleSlotChange}></slot>`;
  }

  render() {
    return this.isLink ?
      html`<a
          role="link"
          class="container ${this.underlined ? "underlined" : ""} ${this.disabled
            ? "disabled"
            : ""}"
          href="${this.href}"
          target="${this.target ? this.target : nothing}"
        >
          <span class="prefix">
            <slot name="prefix" @slotchange=${this.handleSlotChange}></slot>
          </span>
          <span
            class="slot-text ${this.hasPrefixSlot ? "gap-left" : ""} ${this
              .hasSuffixSlot || this.hasBlankTarget
              ? "gap-right"
              : ""}"
          >
            <slot></slot>
          </span>
          <span class="suffix"> ${this.suffix} </span>
        </a>`
      :
      html`<button
        role="button"
        class="container ${this.underlined ? "underlined" : ""} ${this.disabled
        ? "disabled"
        : ""}"
        ?disabled=${this.disabled}
        >
          <span class="prefix">
            <slot name="prefix" @slotchange=${this.handleSlotChange}></slot>
          </span>
          <span
            class="slot-text ${this.hasPrefixSlot ? "gap-left" : ""} ${this
        .hasSuffixSlot || this.hasBlankTarget
        ? "gap-right"
        : ""}"
          >
            <slot></slot>
          </span>
          <span class="suffix"> ${this.suffix} </span>
        </button>`
  }
}

registerElement("snf-link-button", LinkButton);

declare global {
  interface HTMLElementTagNameMap {
    "snf-link-button": LinkButton;
  }
}
