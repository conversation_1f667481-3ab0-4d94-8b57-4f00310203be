@use "@styles/style" as snf;

:host {
  display: inline-block;
  pointer-events: none;
  padding: snf.spacing(1);

  .container {
    display: flex;
    flex: 0 auto;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    user-select: none;
    @include snf.label-large();
    color: snf.color-scheme(primary);
    pointer-events: auto;
    background-color: transparent;
    border-width: 0;
    text-decoration: none;

    &:hover {
      color: snf.color-scheme(on-primary-fixed-variant);
    }

    &:focus,
    &:focus-visible {
      color: snf.color-scheme(primary);
    }

    &:active {
      color: snf.color-scheme(primary);
    }

    &.disabled {
      // disable all hover events
      pointer-events: none;
      cursor: unset;

      color: snf.hex-opacity(
        snf.$color-scheme-primary,
        snf.$state-layer-disable
      );
    }

    &.underlined {
      &:not(:hover) .slot-text {
        text-decoration: underline;
      }

      &:hover .slot-text {
        text-decoration: none;
      }

      &:active .slot-text {
        text-decoration: underline;
      }
    }

    .slot-text {
      display: flex;
      color: inherit;
      font-size: inherit;
      cursor: inherit;
      user-select: inherit;
      pointer-events: inherit;
    }

    .gap-left {
      padding-inline-start: snf.spacing(2);
    }

    .gap-right {
      padding-inline-end: snf.spacing(2);
    }

    .prefix,
    .suffix {
      display: flex;
      align-items: center;

      .icon {
        --icon-cursor: pointer;
      }

      ::slotted(*) {
        --icon-cursor: pointer;
      }
    }
  }
}
