import { html, unsafeCSS } from 'lit';
import { property } from 'lit/decorators.js';
import BaseElement from '../../internals/baseElement/baseElement';
import styles from './info-box.scss?inline';
import { iconForColorScheme } from '../../internals/utils/icon-for-color-scheme';
import { ColorSchemes } from '../../internals/utils/color-scheme';
import '../icon/icon.component';
import registerElement from "../registerElement";

/*
 * This component, although visually very similar to the alert bar, has several differences:
 * - It does not have a close button, but it has an edit button in large size
 * - It uses a different icon for the warning variant
 * - It uses less padding
 * - It has a border in small size
 * - It is always only one line high
 * - It is only ever as large as its content, whereas the alert bar is always as wide as its parent
 * - It has a 'neutral' color variant
 */

export const infoBoxSizes = [
  'small',
  'large',
] as const;
export type InfoBoxSizes = typeof infoBoxSizes[number];

export const actionTypes = [
  'edit',
  'delete',
  'none',
] as const;
export type ActionType = typeof actionTypes[number];

/**
 * @slot slot - Pass the HTML structure that should be displayed inside the info box
 * @color required - Specify the color scheme of the info box (default: neutral)
 * @size required - Specify the size of the info box. Can either be 'large' or 'small'. Small info boxes have a border. (default: large)
 * @showIcon optional - Specify whether the icon on the left side should be displayed (default: false)
 * @actionType optional - Specify the type of action that should be displayed on the right side of the info box. Can either be 'edit', 'delete' or 'none'. (default: none)
 * @actionClick optional - Event emitted when the action button is clicked
 */
export default class InfoBox extends BaseElement {
  static override styles = [
    BaseElement.globalStyles,
    unsafeCSS(styles),
  ];

  @property({ type: String })
  public color: ColorSchemes = 'neutral';

  @property({ type: String })
  public size: InfoBoxSizes = 'large';

  @property({ type: Boolean })
  public showIcon = false;

  @property({ type: String })
  public actionType: ActionType = 'none';
  
  get actionTypeIcon() {
    if (this.actionType === 'none') {
      return null;
    }
    
    const icon = this.actionType === 'edit' ? 'edit' : 'close';

    return html`
      <snf-icon 
      class="icon-right"
      inline
      @click="${this.handleActionClick}"
      @keydown="${this.handleKeyDown}"
      role="button"
      tabindex="0"
      aria-label="${icon}"
      >${icon}</snf-icon>`;
  }

  override render() {
    return html`
      <div class=${`container color-scheme-${this.color} ${this.size === 'small' ? 'border' : ''} ${this.size}`}>
        ${this.showIcon ? html`<snf-icon class="icon-left icon-${this.size}">${iconForColorScheme(this.color)}</snf-icon>` : null}
        <slot class="content content-${this.size}"></slot>
        ${this.actionTypeIcon}
      </div>
    `;
  }
  
  private handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.handleActionClick();
    }
  }

  private handleActionClick() {
    this.dispatchEvent(new CustomEvent('actionClick', { bubbles: true, composed: true }));
  }
}

registerElement('snf-info-box', InfoBox);

declare global {
  interface HTMLElementTagNameMap {
    'snf-info-box': InfoBox;
  }
}
