@use "@styles/style" as snf;

:host {
  display: inline-block;
}

::slotted(*) {
  margin-top: snf.spacing(1);
  margin-bottom: snf.spacing(1);
}

.container {
  padding-right: snf.spacing(2);
  padding-left: snf.spacing(2);
  border-radius: snf.shape(extra-small);

  display: flex;
  flex-direction: row;
  align-items: center;

  &.small {
    min-height: var(--size-4);
  }

  &.large {
    min-height: var(--size-5);
  }

  &.border {
    border-width: var(--stroke-small);
    border-style: solid;
  }

  .icon-left {
    margin-right: snf.spacing(2);

    &.icon-large {
      font-size: var(--typescale-title-medium-size);
    }
  }

  .icon-right {
    margin-left: snf.spacing(2);
    color: var(--color-scheme-on-surface);
    cursor: pointer;
  }

  .content {
    color: var(--color-scheme-on-surface);
    font: var(--font-label-large); // default

    &.content-small {
      font: var(--font-label-small);
    }

    &.content-large {
      font: var(--font-label-large);
    }
  }
}

.color-scheme-neutral {
  background-color: var(--color-scheme-secondary-container);

  &.small {
    border: 1px solid var(--color-scheme-on-secondary-container);
  }

  .content {
    color: var(--color-scheme-on-secondary-container);
  }

  .icon-left {
    color: var(--color-scheme-on-secondary-container);
  }
  
  .icon-right {
    color: var(--color-scheme-on-secondary-container);
  }
}

.color-scheme-success {
  background-color: var(--color-scheme-success-container);

  &.small {
    border: 1px solid var(--color-scheme-success);
  }
  
  .content {
    color: var(--color-scheme-on-success-container);
  }

  .icon-left {
    color: var(--color-scheme-success);
  }

  .icon-right {
    color: var(--color-scheme-on-success-container);
  }
}

.color-scheme-error {
  background-color: var(--color-scheme-error-container);

  &.small {
    border: 1px solid var(--color-scheme-error);
  }
  
  .content {
    color: var(--color-scheme-on-error-container);
  }

  .icon-left {
    color: var(--color-scheme-error);
  }

  .icon-right {
    color: var(--color-scheme-on-error-container);
  }
}

.color-scheme-warning {
  background-color: var(--color-scheme-warning-container);

  &.small {
    border: 1px solid var(--color-scheme-warning);
  }
  
  .content {
    color: var(--color-scheme-on-warning-container);
  }
  
  .icon-left {
    color: var(--color-scheme-warning);
  }

  .icon-right {
    color: var(--color-scheme-on-warning-container);
  }
}

.color-scheme-info {
  background-color: var(--color-scheme-info-container);

  &.small {
    border: 1px solid var(--color-scheme-info);
  }
  
  .content {
    color: var(--color-scheme-on-info-container);
  }

  .icon-left {
    color: var(--color-scheme-info);
  }
  
  .icon-right {
    color: var(--color-scheme-on-info-container);
  }
}
