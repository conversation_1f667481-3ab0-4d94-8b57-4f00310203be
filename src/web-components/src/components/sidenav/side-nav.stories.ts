import docs from "./side-nav.md?raw";
import { html } from "lit-html";
import { styleMap } from "lit-html/directives/style-map.js";
import {
  Meta,
  StoryFn,
  StoryObj,
  WebComponentsRenderer,
} from "@storybook/web-components";
import { withActions } from "@storybook/addon-actions/decorator";
import { SideNav } from "../../index";

import "./side-nav.component";
import "./sidenavitem/side-nav-item.component";
import "./sidenavitem/sidenavitemicon/side-nav-item-icon.component";
import "./sidenavitem/sidenavitemcontent/side-nav-item-content.component";
import "./sidenavitem/sidenavitemsubnav/side-nav-item-sub-nav.component";
import "../infobox/info-box.component";
import { ifDefined } from "lit-html/directives/if-defined.js";

type SideNavStoryArgs = {
  isRoot: boolean;
  collapsible: boolean;
  active: boolean;
  firstChildActive: string;
  activeChild: string;
};

enum Childs {
  contributors = "Contributors asdg asgasfhadshja dfjadfjadjf adgjadgjadg",
  titleandduration = "Title and Durationdajfad jadgfjadgjadgjad jadgjfgsdjdgajadga",
  Relations = "Relations",
}

const childs = Object.values(Childs);

const meta: Meta<SideNavStoryArgs> = {
  title: "Components/Side Nav",
  component: "snf-side-nav",
  argTypes: {
    isRoot: { control: "boolean" },
    collapsible: {
      control: "boolean",
      table: { defaultValue: { summary: "false" } },
    },
    active: { control: "boolean" },
    activeChild: { control: "select", options: childs },
  },
  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
    actions: {
      handles: ["click"],
    },
  },
  decorators: [withActions<WebComponentsRenderer>],
};
export default meta;

const Template: StoryFn<SideNavStoryArgs> = (args) => html`
  <snf-side-nav style=${styleMap({ width: "300px" })}>
    <snf-side-nav-item
      isroot=${ifDefined(args.isRoot)}
      collapsible=${ifDefined(args.collapsible)}
      active=${ifDefined(args.active)}
      href="#Proposals"
    >
      <snf-side-nav-item-content>Proposals with lot of text asdf asagd asgdasdhasfh sah as dfg</snf-side-nav-item-content>
      <snf-side-nav-item-icon
        slot="icon"
        iconStyle="outlined"
        style="${styleMap({ color: "var(--color-scheme-success)" })}"
        >check_circle</snf-side-nav-item-icon
      >
      <snf-side-nav-item-sub-nav slot="submenu">
        ${childs.map(
          (child) => html`<snf-side-nav-item
            active=${args.activeChild === child}
          >
            <snf-side-nav-item-content>
              ${child}
            </snf-side-nav-item-content>
            <snf-side-nav-item-icon
              slot="icon"
              iconStyle="outlined"
              style=${styleMap({ color: "var(--color-scheme-success)" })}
              >check_circle</snf-side-nav-item-icon
            >
            <snf-info-box size="small" color="neutral" slot="second-content">Mandatory</snf-info-box>
          </snf-side-nav-item>`
        )}
      </snf-side-nav-item-sub-nav>
    </snf-side-nav-item>
  </snf-side-nav>
`;

export const Default: StoryObj<SideNavStoryArgs> = {
  render: Template,
};

export const Collabsible: StoryObj<SideNavStoryArgs> = {
  render: Template,
  args: {
    collapsible: true,
    isRoot: true,
  },
};

export const NotCollapsible: StoryObj<SideNavStoryArgs> = {
  render: Template,
  args: {
    collapsible: false,
    isRoot: true,
  },
};

export const Active: StoryObj<SideNavStoryArgs> = {
  render: Template,
  args: {
    active: true,
    isRoot: true,
    activeChild: "Contributors",
  },
};

export const ListWithIcons: StoryObj<SideNav> = {
  render: () => html`
    <snf-side-nav style=${styleMap({ width: "450px" })}>
      <snf-side-nav-item active isroot href="#Proposals">
        <snf-side-nav-item-icon
          slot="icon"
          iconStyle="outlined"
          style=${styleMap({ color: "var(--color-scheme-success)" })}
          >check_circle</snf-side-nav-item-icon
        >
        <snf-side-nav-item-content>Proposals</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible isroot href="#Research">
        <snf-side-nav-item-icon
          slot="icon"
          iconStyle="outlined"
          style=${styleMap({ color: "var(--color-scheme-error)" })}
          >error_outline</snf-side-nav-item-icon
        >
        <snf-side-nav-item-content>Research</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible isroot href="#Submisison">
        <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
          >circle</snf-side-nav-item-icon
        >
        <snf-side-nav-item-content>Submission</snf-side-nav-item-content>
      </snf-side-nav-item>
    </snf-side-nav>
  `,
};

export const ListWithoutIcons: StoryObj<SideNav> = {
  render: () => html`
    <snf-side-nav style=${styleMap({ width: "450px" })}>
      <snf-side-nav-item active isroot href="#Proposals">
        <snf-side-nav-item-content>Proposals</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible isroot href="#Research">
        <snf-side-nav-item-content>Research</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible isroot href="#Submisison">
        <snf-side-nav-item-content>Submission</snf-side-nav-item-content>
      </snf-side-nav-item>
    </snf-side-nav>
  `,
};

export const BigExample: StoryObj<SideNav> = {
  render: () => html`
    <snf-side-nav style=${styleMap({ width: "450px" })}>
      <snf-side-nav-item active href="#proposals" isRoot collapsible>
        <snf-side-nav-item-content>Proposals</snf-side-nav-item-content>
        <snf-side-nav-item-icon
          slot="icon"
          style="${styleMap({ color: "var(--color-scheme-success)" })}"
          >check_circle</snf-side-nav-item-icon
        >
        <snf-side-nav-item-sub-nav slot="submenu">
          <snf-side-nav-item href="#contributors">
            <snf-side-nav-item-content>Contributors</snf-side-nav-item-content>
            <snf-side-nav-item-icon
              slot="icon"
              iconStyle="outlined"
              style=${styleMap({ color: "var(--color-scheme-success)" })}
              >check_circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
          <snf-side-nav-item href="#titleandduration">
            <snf-side-nav-item-content
              >Title and duration</snf-side-nav-item-content
            >
            <snf-side-nav-item-icon
              slot="icon"
              iconStyle="outlined"
              style=${styleMap({ color: "var(--color-scheme-success)" })}
              >check_circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
          <snf-side-nav-item href="#relations">
            <snf-side-nav-item-content>Relations</snf-side-nav-item-content>
            <snf-side-nav-item-icon
              slot="icon"
              iconStyle="outlined"
              style=${styleMap({ color: "var(--color-scheme-success)" })}
              >check_circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
        </snf-side-nav-item-sub-nav>
      </snf-side-nav-item>
      <snf-side-nav-item href="#Research" isroot>
        <snf-side-nav-item-content>Research</snf-side-nav-item-content>
        <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
          >circle</snf-side-nav-item-icon
        >
        <snf-side-nav-item-sub-nav slot="submenu">
          <snf-side-nav-item>
            <snf-side-nav-item-content>Team</snf-side-nav-item-content>
            <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
              >circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
          <snf-side-nav-item>
            <snf-side-nav-item-content>Summary</snf-side-nav-item-content>
            <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
              >circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
          <snf-side-nav-item>
            <snf-side-nav-item-content>Research plan</snf-side-nav-item-content>
            <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
              >circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
          <snf-side-nav-item>
            <snf-side-nav-item-content>Budget</snf-side-nav-item-content>
            <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
              >circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
          <snf-side-nav-item>
            <snf-side-nav-item-content>Declarations</snf-side-nav-item-content>
            <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
              >circle</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
        </snf-side-nav-item-sub-nav>
      </snf-side-nav-item>
      <snf-side-nav-item href="#Submisison" isroot>
        <snf-side-nav-item-content>Submission</snf-side-nav-item-content>
        <snf-side-nav-item-icon
          slot="icon"
          iconStyle="outlined"
          style=${styleMap({ color: "var(--color-scheme-error)" })}
          >error_outline</snf-side-nav-item-icon
        >
        <snf-side-nav-item-sub-nav slot="submenu">
          <snf-side-nav-item>
            <snf-side-nav-item-content>Exclusions</snf-side-nav-item-content>
            <snf-side-nav-item-icon
              slot="icon"
              iconStyle="outlined"
              style=${styleMap({ color: "var(--color-scheme-error)" })}
              >error_outline</snf-side-nav-item-icon
            >
          </snf-side-nav-item>
        </snf-side-nav-item-sub-nav>
      </snf-side-nav-item>
    </snf-side-nav>
  `,
};

export const ListWithExternalLinks: StoryObj<SideNav> = {
  render: () => html`
    <snf-side-nav style=${styleMap({ width: "450px" })}>
      <snf-side-nav-item active isroot href="#Proposals">
        <snf-side-nav-item-content>Proposals</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible isroot href="#Research">
        <snf-side-nav-item-content>Research</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible href="https://portal.snf.ch" onclick="stopImmediatePropagation()">
        <snf-side-nav-item-content>External Link to SNF Portal</snf-side-nav-item-content>
      </snf-side-nav-item>
      <snf-side-nav-item collapsible href="https://portal.snf.ch" onclick="stopImmediatePropagation()">
        <snf-side-nav-item-icon slot="icon" iconStyle="outlined"
        >link</snf-side-nav-item-icon
        >
        <snf-side-nav-item-content>External Link with icon to SNF Portal</snf-side-nav-item-content>
      </snf-side-nav-item>
    </snf-side-nav>
  `,
};
