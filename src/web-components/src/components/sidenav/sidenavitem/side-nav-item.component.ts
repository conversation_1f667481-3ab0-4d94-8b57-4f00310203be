import { html, unsafeCSS } from "lit";
import {
  property,
  queryAssignedElements,
} from "lit/decorators.js";
import BaseElement from "../../../internals/baseElement/baseElement";
import styles from "./side-nav-item.scss?inline";
import SideNavItemSubNav from "./sidenavitemsubnav/side-nav-item-sub-nav.component";
import { booleanAttributeConverter } from "../../../internals/utils/boolean-attribute-converter";
import registerElement from "../../registerElement";

export default class SideNavItem extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  private hasSubmenu(): boolean {
    return this.subMenuItems.length > 0;
  }

  private get classes() {
    return {
      active: this.active,
      rootActive: this.isRoot && this.active,
      rootInactive: this.isRoot && !this.active,
    };
  }

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  isRoot = false;

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  active = false;

  @property({ type: String, reflect: true }) href = "#";

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  isOpen = true;

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  collapsible = false;

  @queryAssignedElements({ slot: "submenu" }) subMenus!: HTMLElement[];

  public get childrenItems(): SideNavItem[] {
    const items: SideNavItem[] = [];
    this.subMenus?.forEach((item) => {
      const itemChildren = item.querySelectorAll("snf-side-nav-item");
      items.push(...itemChildren);
    });
    return items;
  }

  public get subMenuItems(): SideNavItemSubNav[] {
    const items: SideNavItemSubNav[] = [];
    this.subMenus?.forEach((item) => {
      if (item instanceof SideNavItemSubNav) {
        items.push(item);
      } else {
        const subItem = item.querySelector("snf-side-nav-item-sub-nav");
        if (subItem instanceof SideNavItemSubNav) {
          items.push(subItem);
        }
      }
    });
    return items;
  }

  public hasChild(item: SideNavItem): boolean {
    return this.childrenItems.includes(item);
  }

  render() {
    return html`
      <li
        class="container ${this.classes.rootActive ? "root-active" : ""} ${this
          .classes.rootInactive
          ? "root-inactive"
          : ""}"
      >
        <div class="nav-item-content-container">
          <a class="${this.active ? "active" : ""} ${this.isRoot ? 'root' : ''}" href="${this.href}">
            <div class="icon">
              <slot class="icon" name="icon"></slot>
            </div>
            <div class="content">
              <slot></slot>
            </div>
            <div class="second-content">
              <slot name="second-content"></slot>
            </div>
          </a>
          ${this.hasSubmenu() && this.collapsible
            ? html` <button
                class="expand-icon-button"
                aria-label="expand"
                @click=${this.toggleSubmenu}
              >
                <snf-icon
                  class=${`icon-${this.isOpen ? "expanded" : "collapsed"}`}
                  iconSize="medium"
                  >expand_more
                </snf-icon>
                <div class="state-layer"></div>
              </button>`
            : null}
        </div>

        <slot @slotchange="${this.handleSlotChange}" name="submenu"></slot>
      </li>
    `;
  }

  private toggleSubmenu(event: Event) {
    event.stopPropagation();
    this.isOpen = !this.isOpen;
    this.subMenuItems.forEach((subMenu) => {
      subMenu.expanded = this.isOpen;
    });
    this.requestUpdate();
  }

  private handleSlotChange() {
    this.subMenuItems.forEach((subMenu) => {
      if (!this.collapsible) {
        subMenu.expanded = true;
      } else {
        subMenu.expanded = this.isOpen;
      }
    });
    this.requestUpdate();
  }
}

registerElement('snf-side-nav-item', SideNavItem);

declare global {
  interface HTMLElementTagNameMap {
    "snf-side-nav-item": SideNavItem;
  }
}
