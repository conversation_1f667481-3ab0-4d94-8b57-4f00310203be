import './side-nav-item.component';
import { describe, expect, test } from 'vitest';
import { fixture, html, elementUpdated } from '@open-wc/testing-helpers';

describe('SideNavItem', () => {
  test('renders with default properties', async () => {
    const el = await fixture(html`<snf-side-nav-item></snf-side-nav-item>`);
    await elementUpdated(el);
    expect(el).toBeDefined();
  });

  test('renders slot content', async () => {
    const el = await fixture(html`
      <snf-side-nav-item>
        <span slot="icon">icon</span>
        <span>Main Content</span>
        <span slot="second-content">second</span>
        <div slot="submenu">submenu</div>
      </snf-side-nav-item>
    `);

    await elementUpdated(el);

    const iconSlot = el.querySelector('[slot="icon"]');
    const secondContentSlot = el.querySelector('[slot="second-content"]');
    const submenuSlot = el.querySelector('[slot="submenu"]');
    expect(iconSlot?.textContent).toBe('icon');
    expect(secondContentSlot?.textContent).toBe('second');
    expect(submenuSlot?.textContent).toBe('submenu');
  });
});
