@use "@styles/style" as snf;

:host {
  display: block;
}

.root-active {
  border-top: 1px solid var(--color-scheme-error);
}

.root-inactive {
  border-top: 1px solid var(--color-scheme-primary);
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;

  .nav-item-content-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    a {
      @include snf.body-medium();
      color: snf.$color-scheme-primary;
      padding: snf.spacing(1);
      flex-grow: 1;
      display: flex;
      flex-direction: row;

      .icon {
        padding-top: 6px; // align icon with the text, is there a better way to do it?
        margin-right: snf.spacing(1);
        align-self: flex-start;
      }

      .content {
        align-self: center;
      }

      .second-content {
        margin-left: snf.spacing(2);
      }
    }

    .root {
      @include snf.title-large();
    }

    .active {
      padding: snf.spacing(1);
      font-weight: snf.$typeface-weight-bold;
      flex-grow: 1;
    }

    .expand-icon-button {
      position: relative;
      background: none;
      border: 0;
      cursor: pointer;
      padding-top: snf.spacing-mobile(3);
      display: flex;
      align-self: flex-start;
      align-items: center;
      justify-content: center;

      .state-layer {
        position: absolute;
        inset: 0;
        background: none;
        transition: opacity 0.3s;
        cursor: pointer;
      }

      &:hover .state-layer {
        background-color: var(--state-layer-hover);
      }
    }
  }

  .icon-expanded {
    transform: rotate(180deg);
  }

  .submenu-collapsed {
    padding-inline-start: snf.spacing(4);
    display: none;
    list-style: none;
  }

  .submenu-expanded {
    display: block;
  }
}
