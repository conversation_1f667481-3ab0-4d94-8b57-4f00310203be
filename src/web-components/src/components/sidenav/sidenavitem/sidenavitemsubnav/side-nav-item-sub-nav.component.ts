import { html, unsafeCSS } from "lit";
import { property } from "lit/decorators.js";
import BaseElement from "../../../../internals/baseElement/baseElement";
import styles from "./side-nav-item-sub-nav.scss?inline";
import registerElement from "../../../registerElement";

export const colorSchemes = ["neutral", "success", "error", "warning"] as const;
export type ColorSchemes = (typeof colorSchemes)[number];

/**
 * @slot slot - Pass the HTML structure that should be displayed inside the info box
 * @color required - Specify the color scheme of the info box (default: neutral)
 */
export default class SideNavItemSubNav extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({ type: Boolean }) expanded = false;

  render() {
    return html`
      <nav
        role="navigation"
        aria-label="Secondary"
        class="side-nav-submenu-${this.expanded ? "expanded" : "collapsed"}"
      >
        <ul>
          <slot></slot>
        </ul>
      </nav>
    `;
  }
}

registerElement("snf-side-nav-item-sub-nav", SideNavItemSubNav);

declare global {
  interface HTMLElementTagNameMap {
    "snf-side-nav-item-sub-nav": SideNavItemSubNav;
  }
}
