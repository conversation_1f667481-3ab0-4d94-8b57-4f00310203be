import { html, unsafeCSS } from "lit";
import { property } from "lit/decorators.js";
import BaseElement from "../../internals/baseElement/baseElement";
import styles from "./global-banner.scss?inline";
import { ColorSchemes } from "../../internals/utils/color-scheme";
import { iconForColorScheme } from "../../internals/utils/icon-for-color-scheme";
import "../icon/icon.component";
import registerElement from "../registerElement";

/**
 * @slot slot - Pass the HTML structure that should be displayed inside the global banner
 * @color required - Specify the color scheme of the global banner (default: info)
 * @showIcon optional - Specify whether the icon on the left side should be displayed (default: false)
 */
export default class GlobalBanner extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({ type: String })
  public color: ColorSchemes = "info";

  @property({ type: Boolean })
  public hideIcon = false;

  override render() {
    let icon = iconForColorScheme(this.color);
    if (icon == "warning") {
      icon = "warning_amber";
    } else if (icon == "cancel") {
      icon = "error_outline";
    }

    return html`
      <div class=${`container color-scheme-${this.color}`}>
        ${this.hideIcon
          ? null
          : html`<snf-icon class="icon-left icon-large" iconStyle="outlined"
              >${icon}</snf-icon
            >`}
        <slot class="content"></slot>
      </div>
    `;
  }
}

registerElement("snf-global-banner", GlobalBanner);

declare global {
  interface HTMLElementTagNameMap {
    "snf-global-banner": GlobalBanner;
  }
}
