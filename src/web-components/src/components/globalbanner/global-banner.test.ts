import { describe, expect, test, vi } from 'vitest';
import { elementUpdated, fixture, html } from '@open-wc/testing-helpers';
import './global-banner.component';
import { ColorSchemes } from '../../internals/utils/color-scheme';

describe('GlobalBanner', () => {
    test('should display the default global banner with no content when nothing is given', async () => {
      const element: HTMLElementTagNameMap['snf-global-banner'] = await fixture(html`
        <snf-global-banner></snf-global-banner>
      `);
  
      await elementUpdated(element);
      await vi.dynamicImportSettled();
  
      expect(element.shadowRoot?.querySelector('slot')?.assignedNodes({ flatten: true }).length).toBe(0);
      expect(element.color).toBe('info');
      expect(element.hideIcon).toBe(false);
    });

    test.each([
      ['neutral', false],
      ['neutral', true],
      ['info', false],
      ['info', true],
      ['warning', false],
      ['warning', true],
      ['error', false],
      ['error', true],
      ['success', false],
      ['success', true],
    ])('should display as configured', async (color, hideIcon) => {
      const element: HTMLElementTagNameMap['snf-global-banner'] = await fixture(html`
        <snf-global-banner color=${color as ColorSchemes} ?hideIcon=${hideIcon}><span>test</span></snf-global-banner>
      `);
  
      await elementUpdated(element);
      await vi.dynamicImportSettled();
  
      expect(element.color).toBe(color);
      expect(element.hideIcon).toBe(hideIcon);
    });
});
