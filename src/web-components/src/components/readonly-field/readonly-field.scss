@use "@styles/style" as snf;

:host {
  display: inline-block;
  width: 100%;
  min-width: 212px;

  .hint {
    padding-top: snf.spacing(1);
    padding-left: snf.spacing(3);
    font: var(--font-label-small);
    width: max-content;
    min-height: var(--size-3);
    color: snf.hex-opacity(
        snf.$color-scheme-on-surface,
        snf.$state-layer-disable
    );
  }
  
  .container {
    padding: snf.spacing-mobile(3) snf.spacing(3) snf.spacing-mobile(3) snf.spacing(3);
    border-radius: snf.shape(none);
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    min-height: 30px;

    &.border {
      border-radius: snf.shape(none);
      border-width: snf.stroke(small);
      border-style: solid;
      border-color: snf.hex-opacity(
          snf.$color-scheme-on-surface,
          snf.$state-layer-disable
      );
    }

    .label {
      position: absolute;
      top: -.75em;
      // To align the label with the text (padding left (16px) + border (1px))
      left: 12px;
      padding: 0 5px;
      font: var(--font-label-small);
      width: max-content;
      color: snf.hex-opacity(
          snf.$color-scheme-on-surface,
          snf.$state-layer-disable
      );
      background-color: var(--color-scheme-surface-container-lowest);
    }
    
    .content {
      color: var(--color-scheme-on-surface);
      width: inherit;

      &.content-small {
        font: var(--font-body-small);
      }

      &.content-medium {
        font: var(--font-body-medium);
      }
      
      &.content-large {
        font: var(--font-body-large);
      }
      
      &.content-multi-line::slotted(*) {
        white-space: pre-line;
      }

      &.content-single-line::slotted(*) {
        white-space: nowrap;
        overflow: hidden;
        display: block;
        text-overflow: ellipsis;
      }
    }
  }
}
