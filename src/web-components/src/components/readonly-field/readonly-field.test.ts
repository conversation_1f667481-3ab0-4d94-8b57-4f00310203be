import "./readonly-field.component";
import { describe, expect, test, vi } from "vitest";
import { elementUpdated, fixture, html } from "@open-wc/testing-helpers";
import { FieldType, ReadOnlyBoxSizes } from "./readonly-field.component";

describe("ReadOnlyField", () => {
  test("should display nothing when no content and label given", async () => {
    const element: HTMLElementTagNameMap["snf-readonly-field"] = await fixture(
      html` <snf-readonly-field></snf-readonly-field> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      element.shadowRoot
        ?.querySelector("slot")
        ?.assignedNodes({ flatten: true }).length,
    ).toBe(0);
  });

  test("should display the slot content as text content", async () => {
    const element: HTMLElementTagNameMap["snf-readonly-field"] = await fixture(
      html` <snf-readonly-field>home</snf-readonly-field> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(
      element.shadowRoot
        ?.querySelector("slot")
        ?.assignedNodes({ flatten: true }).length,
    ).toBe(1);
    expect(
      element.shadowRoot
        ?.querySelector("slot")
        ?.assignedNodes({ flatten: true })[0].textContent,
    ).toBe("home");
  });

  test("should display label when set", async () => {
    const element: HTMLElementTagNameMap["snf-readonly-field"] = await fixture(
      html` <snf-readonly-field label="test label">home</snf-readonly-field> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const containerElement = element.shadowRoot?.querySelector("div");
    expect(containerElement).not.toBeNull();
    expect(containerElement!.querySelector("div")?.className.trim()).toBe(
      "label",
    );
  });

  test("should ensure fieldType defaults to multi-line and size large", async () => {
    const element: HTMLElementTagNameMap["snf-readonly-field"] = await fixture(
      html`<snf-readonly-field></snf-readonly-field>`,
    );
    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const containerElement = element.shadowRoot?.querySelector("div");
    expect(containerElement).not.toBeNull();
    expect(containerElement!.className.trim()).toBe("container border");
    expect(containerElement!.querySelector("div")?.className.trim()).toBe(
      "content content-large content-multi-line",
    );
  });

  test.each([
    ["multi-line", "content-multi-line"],
    ["single-line", "content-single-line"],
  ])(
    'should ensure fieldType "%s" adds class "%s"',
    async (fieldType, expectedClass) => {
      const element: HTMLElementTagNameMap["snf-readonly-field"] =
        await fixture(
          html`<snf-readonly-field
            fieldType=${fieldType as FieldType}
          ></snf-readonly-field>`,
        );
      await elementUpdated(element);
      await vi.dynamicImportSettled();

      const containerElement = element.shadowRoot?.querySelector("div");
      expect(containerElement).not.toBeNull();
      expect(
        containerElement!.querySelector("div")?.className.trim(),
      ).toContain(expectedClass);
    },
  );

  test.each([
    ["small", "content-small"],
    ["medium", "content-medium"],
    ["large", "content-large"],
  ])('should ensure size "%s" adds class "%s"', async (size, expectedClass) => {
    const element: HTMLElementTagNameMap["snf-readonly-field"] = await fixture(
      html`<snf-readonly-field
        size=${size as ReadOnlyBoxSizes}
      ></snf-readonly-field>`,
    );
    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const containerElement = element.shadowRoot?.querySelector("div");
    expect(containerElement).not.toBeNull();
    expect(containerElement!.querySelector("div")?.className.trim()).toContain(
      expectedClass,
    );
  });
});
