import { html, unsafeCSS } from 'lit';
import { property } from 'lit/decorators.js';
import BaseElement from '../../../internals/baseElement/baseElement';
import styles from './bread-crumb-item.scss?inline';
import registerElement from "../../registerElement";

export const BreadCrumbItemTypes = ["link", "text"] as const;
export type BreadCrumbItemType = typeof BreadCrumbItemTypes[number];

/**
 * @slot - Add text to create the breadcrumb item.
 * @href - Add a link to the breadcrumb item.
 */
export default class BreadCrumbItem extends BaseElement {
  static override styles = [
    BaseElement.globalStyles,
    unsafeCSS(styles),
  ];
  
  @property({ type: String, reflect: true })
  public href = '#';
  
  @property({ type: String })
  public type: BreadCrumbItemType = 'link';

  override render() {
    return this.type === 'link'
      ?
      html`
      <a href="${this.href}">
        <slot></slot>
      </a>` 
      : 
      html`<p><slot></slot></p>`;
  }
}

registerElement('snf-bread-crumb-item', BreadCrumbItem);

declare global {
  interface HTMLElementTagNameMap {
    'snf-bread-crumb-item': BreadCrumbItem;
  }
}
