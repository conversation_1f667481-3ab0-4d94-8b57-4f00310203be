@use "@styles/style" as snf;

:host {
  display: block;
  overflow: hidden;

  p {
    margin: snf.spacing(2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  a {
    all: unset;
    @include snf.label-large();
    cursor: pointer;
    white-space: nowrap;
    border-radius: 100px;
    padding: snf.spacing(1) snf.spacing(3);
    background-color: unset;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    color: snf.color-scheme(primary);

    &:hover {
      background-color: snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-hover);
      transition: background-color 0.3s ease-in-out;
    }

    &:focus, &:focus-visible {
      background-color: snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-focus);
      transition: background-color 0.3s ease-in-out;
    }

    &:active {
      background-color: snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-press);
    }
  }
}
