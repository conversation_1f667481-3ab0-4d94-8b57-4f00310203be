:host {
  display: inline-block;
}

:host([loading]) {
  pointer-events: none;
}

button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  gap: var(--size-1);
  cursor: pointer;
  border: var(--border-medium) solid;
  border-radius: var(--radius-4);
  line-height: var(--typography-main-line-height);
}

button:disabled {
  background: var(--color-universal-grey-10);
  border-color: var(--color-universal-grey-10);
  color: var(--color-universal-grey-75);
  cursor: not-allowed;
}

button:disabled:focus-visible {
  outline: none;
}

button:not(:disabled):focus-visible {
  outline: var(--color-brand-interaction-100) solid .1rem;
  box-shadow: var(--effect-shadow-glow-focus);
}

.type-primary {
  border-color: var(--color-brand-brand-100);
  background: var(--color-brand-brand-100);
  color: var(--color-brand-white-100);
  --spinner-background-color: var(--color-brand-white-100);
}

.type-primary:not(:disabled,:active):hover {
  border-color: transparent;
  background: var(--color-brand-brand-75);
}

.type-secondary {
  border-color: var(--color-brand-interaction-100);
  background: var(--color-brand-interaction-100);
  color: var(--color-brand-white-100);
  --spinner-background-color: var(--color-brand-white-100);
}

.type-secondary:not(:disabled,:active):hover {
  border-color: transparent;
  background: var(--color-brand-interaction-75);
}

.type-ghost {
  border-color: var(--color-brand-interaction-100);
}

.type-icon-only {
  border: none;
}

.type-icon-only,
.type-ghost {
  background: transparent;
  color: var(--color-brand-interaction-100);
}

.type-ghost:disabled {
  border-color: var(--color-universal-grey-25);
}

.type-icon-only:disabled {
  border-color: transparent;
}

:is(.type-ghost,.type-icon-only):disabled {
  background: transparent;
  color: var(--color-universal-grey-75);
}

/* This style is split into two, since host-context is not being implemented outside Chromium browsers.
   In other browsers this would break the whole hover effect. We need host-context here since there is no other way to
   have the button trigger the hover state when inside an input that is being hovered */
:host-context(dss-input:hover) .type-icon-only:not(:disabled) {
  background: var(--color-brand-interaction-25);
}

.type-icon-only:not(:disabled,:active):hover,
.type-ghost:not(:disabled,:active):hover {
  background: var(--color-brand-interaction-25);
}

.spacing-text {
  padding: var(--size-0-5) var(--size-2);
}

.spacing-icon {
  padding: calc(var(--size-0-5) - var(--border-medium));
}

.spacing-icon-only {
  padding: var(--size-0-25);
}

.remove-border-left,
.remove-radius-left {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.remove-border-right,
.remove-radius-right {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.remove-border-all,
.remove-radius-all {
  border-radius: 0;
}

.remove-border-left {
  border-left-width: 0;
}

.remove-border-right {
  border-right-width: 0;
}

.remove-border-all {
  border-width: 0;
}
