## Interaction

* A button has 4 states: enabled, disabled, hover and pressed.
* A button (like all interactive elements) can also have the focus.

## Usage

* Text buttons should be used for meaningful actions. Actions may or may not trigger a navigation.
* Primary buttons
    * Primary buttons should be used for the default action of a page. The default action should be the most
      sensible action.
    * Per page or dialog, only one primary button should be displayed.
* Secondary buttons
    * Are the default style of button to be used for actions.
* Ghost buttons
    * Ghost buttons may be used in situations where there is less attention required. Typically, actions that are only
      necessary in special situations.
    * Often useful with in a form to allow the reading flow from top to bottom (no visual interrupt)
* Link buttons
    * Use within the flow of a form or card.
    * Lowest presence, therefore use for actions tightly coupled with the section where they reside.
* Icon buttons
    * Use icon only buttons within lists or tables.
    * Use icon only buttons in combination with other components to extend their functionality.
    * Use icon buttons (with border) on the top of widgets, when regular buttons are too big.
    * Always add a tool tip description!
    * Carefully select the icon used.