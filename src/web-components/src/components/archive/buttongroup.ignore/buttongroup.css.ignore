:host {
  display: grid;
  align-items: center;
  grid-column-gap: var(--size-1);
}

:host([labelPlacement="top"]) {
  grid-template-areas: "label"
                        "buttongroup"
                        "hint";
}

:host([labelPlacement="left"]) {
  grid-template-columns: auto 1fr;
  grid-template-areas: "label buttongroup"
                      ". hint";
}

:host([labelPlacement="right"]) {
  grid-template-columns: auto 1fr;
  grid-template-areas: "buttongroup label"
                      "hint hint";
}

:host dss-label {
  grid-area: label;
}

:host dss-hint {
  grid-area: hint;
}

.button-wrapper {
  display: flex;
  grid-area: buttongroup;
}

label {
  color: var(--color-brand-interaction-100);
  font-size: var(--typography-small-font-size);
  line-height: var(--typography-small-line-height);
  font-weight: var(--typography-small-font-weight);
  display: block;
  padding-left: var(--size-1);
  margin-bottom: var(--size-0-5);
}
