:host {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-4);
  background-image: linear-gradient(
    90deg,
    var(--color-universal-grey-10) 25%,
    var(--color-brand-white-100) 50%,
    var(--color-universal-grey-10) 75%
  );
  background-size: 400% 100%;
}

@media (prefers-reduced-motion: no-preference) {
  :host {
    animation: loading-wave 2s ease-in-out infinite;
  }

  @keyframes loading-wave {
    from {
      background-position-x: 0;
    }
    to {
      background-position-x: -150%;
    }
  }
}
