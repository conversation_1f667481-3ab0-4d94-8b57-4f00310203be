## Interaction

* Text fields provide a clear affordance for data input (exception is the disabled state).
* Inputs without data, show the placeholder text if set.
* The format (high comma, spaces etc.) is only applied after leaving the field.

## Usage

* Choose the right type of input field depending on the data to be entered (text, number, tel, email etc.)
* Normally they are used in forms and dialogs
