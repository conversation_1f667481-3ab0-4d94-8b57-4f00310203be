:host {
  display: flex;
}

svg {
  animation: spin 2s cubic-bezier(0.17, 0.73, 0.39, 1.25) infinite;
}

:host([type="primary"]) circle {
  stroke: var(--spinner-background-color, var(--color-brand-brand-50));
}

:host([type="primary"]) path {
  stroke: var(--color-brand-brand-100);
}

:host([type="secondary"]) circle {
  stroke: var(--spinner-background-color, var(--color-brand-interaction-25));
}

:host([type="secondary"]) path {
  stroke: var(--color-brand-interaction-100);
}

:host([type="ghost"]) circle {
  stroke: var(--spinner-background-color, var(--color-universal-grey-10));
}

:host([type="ghost"]) path {
  stroke: var(--color-universal-grey-75);
}

:host([type="success"]) circle {
  stroke: var(--spinner-background-color, var(--color-universal-green-25));
}

:host([type="success"]) path {
  stroke: var(--color-universal-green-100);
}

:host([size="small"]) {
  width: var(--size-2);
  height: var(--size-2);
}

:host([size="medium"]) {
  width: var(--size-3);
  height: var(--size-3);
}

:host([size="stretch"]) {
  width: 100%;
  height: 100%;
}

@keyframes spin {
  from {
    transform: rotate(-45deg);
  }
  to {
    transform: rotate(315deg);
  }
}
