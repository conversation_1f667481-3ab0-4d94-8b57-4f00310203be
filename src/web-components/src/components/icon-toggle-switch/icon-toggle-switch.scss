@use "@styles/style" as snf;

:host {
  display: block;

  .toggle-switch {
    /* stylelint-disable-next-line declaration-property-value-disallowed-list */
    border: none;
    height: 40px;
    background-color: var(--color-scheme-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: snf.spacing(3);
    border-radius: snf.spacing(5);
    padding: 0 snf.spacing(1);
    width: 88px;
    cursor: pointer;

    &-hidden {
      opacity: 0;
      cursor: pointer;
      pointer-events: auto  ;
    }
    
    &-icon {
      color: var(--color-scheme-white);
      background-color: transparent;

      &:focus {
        box-shadow: 0 0 0 snf.spacing(2) snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
        border-radius: 50%;
      }
    }

    &.toggle-on {
      background-color: var(--color-scheme-success);
    }

    &.toggle-off {
      background-color: var(--color-scheme-error);
    }

    &.disabled {
      // disable all hover events
      background-color: var(--color-scheme-surface-container);
      pointer-events: none;
      cursor: unset;

      .toggle-switch-icon {
        color: snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
      }
    }
  }
}
