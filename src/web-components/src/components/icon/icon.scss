@use "@styles/icons/icons" as icons;

:host {
  display: inherit;
  height: var(--icon-height); // this is needed to fix the bounding box of the icon component
}

.icon {
  color: inherit;
}

.icon-small {
  @include icons.icon-small;
}

.icon-medium {
  @include icons.icon-medium;
}

.icon-large {
  @include icons.icon-large;
}

.inline {
  font-size: inherit;
  height: inherit;
  line-height: inherit;
  width: inherit;
}
