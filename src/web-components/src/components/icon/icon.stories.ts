/* eslint-disable @typescript-eslint/no-explicit-any */
import Icon, { iconSizes, iconStyles } from './icon.component';
import docs from './icon.md?raw';
import { html } from 'lit-html';
import { Meta, StoryFn, StoryObj, WebComponentsRenderer } from '@storybook/web-components';
import { unsafeHTML } from 'lit-html/directives/unsafe-html.js';
import { withActions } from '@storybook/addon-actions/decorator';
import { ifDefined } from "lit-html/directives/if-defined.js";
import { grid } from '@lit-labs/virtualizer/layouts/grid.js';
import '@lit-labs/virtualizer';

const meta: Meta<Icon> = {
  title: 'Components/Icon',
  component: 'snf-icon',
  argTypes: {
    iconStyle: { control: 'select', options: iconStyles },
    iconSize: { control: 'select', options: iconSizes },
    inline: { control: 'boolean' },
  },
  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
    actions: {
      handles: ['click'],
    },
  },
  decorators: [withActions<WebComponentsRenderer>],
};
export default meta;

const Template: StoryFn<Icon> = ({
                                     iconStyle,
                                     iconSize,
                                     inline,
                                     slot
                                   }) => html`
  <snf-icon iconStyle=${ifDefined(iconStyle)} ?inline=${inline} iconSize=${ifDefined(iconSize)}>
    ${unsafeHTML(slot)}
  </snf-icon>
`;

export const Default: StoryObj<Icon> = {
  render: Template,
  args: {
    slot: 'home',
  },
};

export const Inline: StoryObj<Icon> = {
  render: Template,
  args: {
    slot: 'home',
    inline: true,
  },
};

export const Outlined: StoryObj<Icon> = {
  render: Template,
  args: {
    slot: 'home',
    iconStyle: 'outlined',
  },
};

export const Round: StoryObj<Icon> = {
  render: Template,
  args: {
    slot: 'home',
    iconStyle: 'round',
  },
};

export type IconStory = Icon & { color: string };
const ICONS = ['home', 'chevron_right', 'check_circle', 'warning', 'cancel', 'info'];

export const IconGallery: StoryObj<IconStory> = {
  render: ({ color }) => html`
    This is a gallery of icons extracted from the Material Icons font. Please add some more icons to the list if you need them.
    <style>
      .icon-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-radius: var(--shape-extra-small);
        box-shadow: var(--elevation-1);
        color: ${color};
        word-break: break-all;
        overflow: hidden;
      }

      .icon-wrapper div {
        padding: 3.2rem 0;
        flex: none;
        display: flex;
        justify-content: center
      }

      .icon-wrapper p {
        background-color: var(--color-scheme-surface);
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    </style>
    <lit-virtualizer
      .layout=${grid({
        gap: '20px',
        itemSize: {
          width: '160px',
          height: '160px',
        },
      }) as any}
      .items=${ICONS}
      .renderItem=${((icon: string) => html`
        <div class="icon-wrapper">
          <div>
            <snf-icon>${icon}</snf-icon>
          </div>
          <p>
            ${icon}
          </p>
        </div>
      `) as any}
    ></lit-virtualizer>
    </div>
  `,
  parameters: {
    chromatic: { disableSnapshot: true },
  },
  args: {
    color: '#000',
  },
};
