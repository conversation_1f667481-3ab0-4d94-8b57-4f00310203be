import { html, unsafeCSS } from 'lit';
import { property } from 'lit/decorators.js';
import BaseElement from '../../internals/baseElement/baseElement';
import styles from './callout.scss?inline';
import { iconForColorScheme } from '../../internals/utils/icon-for-color-scheme';
import '../buttons/icon-button/icon-button.component';
import registerElement from "../registerElement";

export const calloutTypes = [
  'error',
  'warning',
  'info',
  'success',
] as const;
export type CalloutType = typeof calloutTypes[number];

/**
 * @slot slot - Pass the HTML structure that should be displayed inside the callout
 * @type required - Specify the type of the callout (error, warning, info, success)
 * @showCloseIcon optional - Specify whether the close icon on the right side should be displayed (default: false)
 * @close optional - Event emitted when the close button is clicked
 */
export default class Callout extends BaseElement {
  static override styles = [
    BaseElement.globalStyles,
    unsafeCSS(styles),
  ];

  @property({ type: String })
  public type: CalloutType = 'info';

  @property({ type: Boolean })
  public showCloseIcon = false;

  override render() {
    return html`
      <div class=${`container color-scheme-${this.type}`}>
        <snf-icon class="icon-left" iconSize="medium">${iconForColorScheme(this.type)}</snf-icon>
        <div class="content">
          <slot></slot>
        </div>
        ${this.showCloseIcon ? html`
          <snf-icon-button 
            class="icon-right"
            iconsize='small'
            @click="${this.handleCloseClick}"
            @keydown="${this.handleKeyDown}"
            aria-label="close"
          >close</snf-icon-button>
        ` : null}
      </div>
    `;
  }
  
  private handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.handleCloseClick();
    }
  }

  private handleCloseClick() {
    this.dispatchEvent(new CustomEvent('close', { bubbles: true, composed: true }));
  }
}

registerElement('snf-callout', Callout);

declare global {
  interface HTMLElementTagNameMap {
    'snf-callout': Callout;
  }
}
