import { html, unsafeCSS } from 'lit';
import { property } from 'lit/decorators.js';
import BaseElement from '../../../internals/baseElement/baseElement';
import styles from './status-badge.scss?inline';
import "../../icon/icon.component";
import registerElement from "../../registerElement";

/**
 * The naming convention for the color schemes is not consistent with the color schemes
 * of the design system because the usage of the status badge is different in the context of
 * the SCS.
 * For example, the color scheme 'red' in the design system is used for error messages or 'warning', but
 * in the context of the status badge, the color scheme 'red' is used for 'rejected'.
 */
export const colorSchemes = [
  'green',
  'yellow',
  'blue',
  'red',
  'purple',
  'grey',
] as const;
export type ColorScheme = typeof colorSchemes[number];

export const badgeVariants = [
  'outlined', 'flat'
] as const;
export type BadgeVariant = typeof badgeVariants[number];

/**
 * @slot slot - Pass the HTML structure that should be displayed inside the status badge
 * @color required - Specify the color scheme of the status badge (default: green)
 * @disabled required - Specify whether the element is disabled (default: false)
 * @variant required - There are two variants to choose from: 'outlined' and 'flat' (default: outlined)
 * @chevron optional - Specify whether the element has a chveron for as a suffix or not (default: false)
 * @open optional - Specify whether the element is open or closed. This is only used if the chveron is activ (default: false)
 */
export default class StatusBadge extends BaseElement {
  static override styles = [
    BaseElement.globalStyles,
    unsafeCSS(styles),
  ];

  @property({ type: String })
  public color: ColorScheme = 'green';
  
  @property({ type: Boolean })
  public disabled: boolean = false;
  
  @property({ type: String })
  public variant: BadgeVariant = 'outlined';

  @property({ type: Boolean })
  public chevron: boolean = false;

  @property({ type: Boolean })
  public open: boolean = false;

  getChevronRender() {
    if (this.chevron) {
      return html`<snf-icon class="icon" iconstyle="filled" iconsize="small"
          >${ this.open ? 'arrow_drop_up' : 'arrow_drop_down'} </snf-icon
        >`;
    } else {
      return html``;
    }
  }

  toggleOpen() {
    if (this.chevron && !this.disabled) {
      this.open = !this.open;
      this.dispatchEvent(new CustomEvent('status-open-toggle', {
        detail: { open: this.open },
        bubbles: true,
        composed: true
      }));
    }
  }

  override render() {
    return this.chevron ?
      html`
        <button @click="${this.toggleOpen}" class=${`container chevron color-scheme-${this.color}
                badge-variant-${this.variant}-${this.disabled ? 'disabled' : 'enabled'}`} 
                ?disabled=${this.disabled}
        >
          <span class="dot"></span>
          <slot></slot>
          <span>${this.getChevronRender()}</span>
        </button>
      ` :
      html`
        <div @click="${this.toggleOpen}" class=${`container color-scheme-${this.color}
         badge-variant-${this.variant}-${this.disabled ? 'disabled' : 'enabled'}
         ${this.chevron ? 'chevron' : ''}`}>
          <span class="dot"></span>
          <slot></slot>
          <span>${this.getChevronRender()}</span>
        </div>
    `;
  }
}

registerElement('snf-status-badge', StatusBadge);

declare global {
  interface HTMLElementTagNameMap {
    'snf-status-badge': StatusBadge;
  }
}
