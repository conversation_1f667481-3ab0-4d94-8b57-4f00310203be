@use "@styles/style" as snf;

:host {
  display: block;

  button {
    all: unset;
  }

  .container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-radius: 40px;
    max-width: fit-content;
    text-wrap: nowrap;
    padding: snf.spacing(1) snf.spacing(2);
    @include snf.label-large();
    color: snf.$color-scheme-on-surface;

    .dot {
      display: inline-block;
      height: 10px;
      width: 10px;
      border-radius: 50%;
    }

    &.chevron {
      cursor: pointer;

      .icon {
        --icon-cursor: pointer;
      }
    }

    &.badge-variant-outlined-enabled {
      border: snf.stroke(small) solid snf.color-scheme(outline-variant);
      
      &:focus, &:focus-visible {
        outline: snf.stroke(medium) solid snf.color-scheme(primary);
        outline-offset: -1px;
      }
    }

    &.badge-variant-outlined-disabled {
      // disable all hover events
      pointer-events: none;
      cursor: unset;
      color: snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-disable);
      border: snf.$stroke-small solid snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-disable);
    }

    &.badge-variant-flat-disabled {
      // disable all hover events
      pointer-events: none;
      cursor: unset;
      color: snf.hex-opacity( snf.$color-scheme-on-surface, snf.$state-layer-disable );
    }

    &.color-scheme-green {
      .dot {
        background-color: var(--color-scheme-success);
      }

      &.chevron:hover {
        background-color: snf.hex-opacity(snf.$color-scheme-success, snf.$state-layer-hover);
      }
    }

    &.color-scheme-blue {
      .dot {
        background-color: snf.color-scheme(info);
      }

      &.chevron:hover {
        background-color: snf.hex-opacity(snf.$color-scheme-info, snf.$state-layer-hover);
      }
    }

    &.color-scheme-yellow {
      .dot {
        background-color: snf.color-scheme(warning);
      }

      &.chevron:hover {
        background-color: snf.hex-opacity(snf.$color-scheme-warning, snf.$state-layer-hover);
      }
    }

    &.color-scheme-red {
      .dot {
        background-color: var(--color-scheme-error);
      }

      &.chevron:hover {
        background-color: snf.hex-opacity(snf.$color-scheme-error, snf.$state-layer-hover);
      }
    }

    &.color-scheme-purple {
      .dot {
        background-color: snf.color-scheme('purple');
      }

      &.chevron:hover {
        background-color: snf.hex-opacity(snf.$color-scheme-purple, snf.$state-layer-hover);
      }
    }

    &.color-scheme-grey {
      .dot {
        background-color: snf.color-scheme(secondary);
      }

      &.chevron:hover {
        background-color: snf.hex-opacity(snf.$color-scheme-secondary, snf.$state-layer-hover);
      }
    }
  }
}
