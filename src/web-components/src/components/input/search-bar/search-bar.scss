@use "@styles/style" as snf;

:host {
  display: inline-block;
  width: 100%;
  min-width: 120px;

  .search-bar-container {
    display: flex;
    align-items: center;
    background-color: transparent;

    border: snf.stroke(small) solid snf.color-scheme(outline);
    border-radius: snf.shape(full);
    padding: snf.spacing(1) snf.spacing(2);
    box-sizing: border-box;
    outline: none;

    &:focus-within:not(.disabled):not(.error),
    &:focus:not(.disabled):not(.error) {
      outline: snf.stroke(medium) solid snf.color-scheme(primary);
      outline-offset: -1px;
    }

    &.error {
      border-color: snf.color-scheme(error);
      outline: snf.stroke(medium) solid snf.color-scheme(error);
      outline-offset: -1px;
    }

    button {
      all: unset;
      border-radius: 50%;

      &:focus-visible, &:focus {
        background-color: snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
      }
    }


    .search-icon, .delete-icon {
      color: snf.color-scheme(primary);
    }

    .search-icon {
      margin-right: snf.spacing(2);
    }

    .delete-icon {
      cursor: pointer;
    }

    &.disabled {
      border-color: snf.hex-opacity(snf.$color-scheme-outline, snf.$state-layer-disable);
      cursor: not-allowed;

      .search-icon, .delete-icon {
        color: snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-disable);
        cursor: not-allowed;
      }
    }

    input {
      flex: 1;
      border: 0;
      background: transparent;
      outline: none;
      padding: 0;
      @include snf.label-large();
      color: snf.color-scheme(on-surface);

      &::placeholder {
        color: snf.color-scheme(on-surface-variant);
      }

      &:disabled {
        cursor: not-allowed;
        color: snf.hex-opacity(snf.$color-scheme-primary, snf.$state-layer-disable);

        &::placeholder {
          color: snf.hex-opacity(snf.$color-scheme-on-surface-variant, snf.$state-layer-disable);
        }
      }
    }
  }
}
