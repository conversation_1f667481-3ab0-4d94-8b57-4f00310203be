@use "@styles/style" as snf;

:host {
  display: block;

  .expansion-panel {
    border: snf.stroke(small) solid snf.color-scheme(outline-variant);
    border-radius: snf.shape(none);
    overflow: hidden;

    &.has-error {
      border-color: snf.color-scheme(error);
    }
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    gap: 16px;
    padding: snf.spacing(3) snf.spacing(4);
    cursor: pointer;
    background-color: snf.color-scheme(secondary-container);
    color: snf.color-scheme(on-secondary-container);

    &.dark-mode {
      background-color: snf.color-scheme(expansion-panel);
      color: snf.color-scheme(on-expansion-panel);
    }

    &.disabled {
      cursor: default;
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .sub-title {
        @include snf.body-medium();
      }
    }

    .main-header {
      display: flex;
      flex-direction: row;
      gap: 16px;

      .title {
        @include snf.title-medium();
        display: flex;
        align-items: center;
      }

      .title-content {
        flex: 1;
        @include snf.title-small();
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: snf.$gap-xs;
    }

    .action-items {
      display: flex;
      justify-self: flex-end;
      gap: snf.$gap-sm;
    }

    .chevron {
      color: snf.color-scheme(primary);
      transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
      transform: rotate(0deg);
      --icon-cursor: pointer;

      &.dark-mode {
        color: snf.color-scheme(on-expansion-panel);
      }

      &.expanded {
        transform: rotate(180deg);
      }

      &.hidden {
        display: none;
      }
    }
  }

  .content-wrapper {
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    max-height: 0;

    .content {
      padding: 0 snf.spacing(4) snf.spacing(3) snf.spacing(4);
    }
  }
}

