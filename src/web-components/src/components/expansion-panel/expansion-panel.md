## Usage

The `expansion-panel` component is used to create collapsible content sections with smooth animations. It's perfect for organizing information in a compact, user-friendly way.

```html
<snf-expansion-panel>
  <div slot="title">Panel Title</div>
  <div slot="title-content">Content on the right side of the Title</div>
  <div slot="action-items">Action items that are aligned on the right side of the header</div>
  <div slot="sub-title">Content bellow the Title</div>
  <div>Panel content goes here...</div>
</snf-expansion-panel>
```

## Technical note

- The component has two slots: `header` for the clickable header content and `content` for the collapsible content area.
- The property `expanded` controls whether the panel is open (default: false).
- The property `disabled` prevents interaction when set to true (default: false).
- When toggled, a 'toggle' event is emitted with `detail: { expanded: boolean }`.
- The component includes smooth CSS transitions for both the content expansion and chevron rotation.
- Supports keyboard navigation with Enter and Space keys.
- Includes proper ARIA attributes for accessibility.
- Uses ResizeObserver to handle dynamic content changes during expansion.

## Accessibility

- The header has `role="button"` for screen readers
- Includes `aria-expanded` to indicate the current state
- Includes `aria-disabled` when the component is disabled
- Supports keyboard navigation with <PERSON>ter and Space keys
- Proper focus management with visible focus indicators
