import { html, unsafeCSS } from 'lit';
import { property, state } from 'lit/decorators.js';
import BaseElement from '../../internals/baseElement/baseElement';
import styles from './expansion-panel.scss?inline';
import '../icon/icon.component';
import registerElement from '../registerElement';
import { Theme } from "../../internals/utils/theme";

/**
 * @slot title - Content to display in the expansion panel main title
 * @slot title-content - Content to display on the right side of the main title
 * @slot sub-title - Content to display in the expansion panel sub title
 * @slot default - Content to display in the expansion panel content area
 * @expanded optional - Whether the panel is expanded (default: false)
 * @hasError optional - Whether the panel has an error (default: false)
 * @disabled optional - Whether the panel is disabled (default: false)
 * @toggleExpansionPanel optional - Event emitted when the panel is expanded or collapsed
 */
export default class ExpansionPanel extends BaseElement {
  static override styles = [
    BaseElement.globalStyles,
    unsafeCSS(styles),
  ];

  @property({ type: Boolean, reflect: true })
  public expanded = false;

  @property({ type: Boolean, reflect: true })
  public hasError = false;

  @property({ type: Boolean, reflect: true })
  public disabled = false;

  @property({ type: String, reflect: true })
  public theme: Theme = 'light';

  @state()
  private contentHeight = 0;

  @state()
  private isAnimating = false;

  private contentElement?: HTMLElement;
  private resizeObserver?: ResizeObserver;

  override firstUpdated() {
    this.contentElement = this.shadowRoot?.querySelector('.content') ?? undefined;

    if (this.contentElement) {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.expanded && !this.isAnimating) {
          this.updateContentHeight();
        }
      });
      this.resizeObserver.observe(this.contentElement);
    }

    this.updateContentHeight();
  }

  override disconnectedCallback() {
    super.disconnectedCallback();
    this.resizeObserver?.disconnect();
  }

  private updateContentHeight() {
    if (this.contentElement) {

      this.contentElement.style.height = 'auto';

      const computedStyle = window.getComputedStyle(this.contentElement);
      const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
      const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
      const borderTop = parseFloat(computedStyle.borderTopWidth) || 0;
      const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0;
      const totalHeight = this.contentElement.scrollHeight + paddingTop + paddingBottom + borderTop + borderBottom;

      this.contentHeight = Math.ceil(totalHeight);
    }
  }

  private toggle() {
    if (this.disabled) {
      return;
    }

    this.isAnimating = true;
    this.updateContentHeight();
    this.expanded = !this.expanded;

    this.dispatchEvent(new CustomEvent('toggleExpansionPanel', {
      bubbles: true,
      composed: true,
      detail: { value: this.expanded }
    }));

    setTimeout(() => {
      this.isAnimating = false;
    }, 300); // Match the CSS transition duration
  }

  private handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggle();
    }
  }

  override render() {
    const contentStyles = this.expanded
      ? `max-height: ${this.contentHeight}px;`
      : 'max-height: 0px;';

    return html`
      <div class="expansion-panel ${this.hasError ? 'has-error' : ''}">
        <div 
          class="header ${this.expanded ? 'expanded' : ''} ${this.disabled ? 'disabled' : ''} ${this.theme === 'dark' ? 'dark-mode' : ''}"
          @click="${this.toggle}"
          @keydown="${this.handleKeyDown}"
          tabindex="0"
          role="button"
          aria-expanded="${this.expanded}"
        >
          <div class="header-content">
            <div class="main-header">
              <div class="title">
                <slot name="title"></slot>
              </div>
              <div class="title-content">
                <slot name="title-content"></slot>
              </div>
            </div>
            <div class="sub-title">
              <slot name="sub-title"></slot>
            </div>
          </div>
          <div class="header-actions">
            <div class="action-items">
              <slot name="action-items"></slot>
            </div>
            <snf-icon
              class="chevron ${this.expanded ? 'expanded' : ''} ${this.disabled ? 'hidden' : ''} ${this.theme === 'dark' ? 'dark-mode' : ''}"
              iconSize="medium"
            >
              expand_more
            </snf-icon>
          </div>
        </div>
        <div 
          class="content-wrapper"
          style="${contentStyles}"
        >
          <div class="content">
            <slot></slot>
          </div>
        </div>
      </div>
    `;
  }
}

registerElement('snf-expansion-panel', ExpansionPanel);

declare global {
  interface HTMLElementTagNameMap {
    'snf-expansion-panel': ExpansionPanel;
  }
}
