import { describe, expect, test, vi } from "vitest";
import { elementUpdated, fixture, html } from "@open-wc/testing-helpers";
import "./expansion-panel.component";

describe("ExpansionPanel", () => {
  test("should display the default expansion panel in collapsed state", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <div slot="title">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    expect(element.expanded).toBe(false);
    expect(element.shadowRoot?.querySelector(".header")).toBeTruthy();
    expect(element.shadowRoot?.querySelector(".content-wrapper")).toBeTruthy();
    expect(element.shadowRoot?.querySelector(".chevron.expanded")).toBeFalsy();
  });

  test("should display content in header and content slots", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <span slot="title">Test Header</span>
        <span>Test Content</span>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const slottedElements = element.shadowRoot?.querySelector("slot",) as HTMLSlotElement | null;
    expect(slottedElements?.assignedElements({ flatten: true })[0].outerHTML).toEqual("<span slot=\"title\">Test Header</span>");

    const contentElements = element.shadowRoot?.querySelector(".content slot",) as HTMLSlotElement | null;
    expect(contentElements?.assignedElements({ flatten: true })[0].outerHTML).toEqual("<span>Test Content</span>");
  });

  test("should expand when header is clicked", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <div slot="title">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const header = element.shadowRoot?.querySelector(".header");
    expect(header).toBeTruthy();

    expect(element.expanded).toBe(false);

    if (header) {
      header.dispatchEvent(new Event("click"));
      await elementUpdated(element);

      expect(element.expanded).toBe(true);
      expect(element.shadowRoot?.querySelector(".chevron.expanded")).toBeTruthy();
    }
  });

  test("should emit toggle event when expanded/collapsed", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <div slot="title">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const toggleSpy = vi.fn();
    element.addEventListener("toggleExpansionPanel", toggleSpy);

    const header = element.shadowRoot?.querySelector(".header");

    if (header) {
      header.dispatchEvent(new Event("click"));
      await elementUpdated(element);

      expect(toggleSpy).toHaveBeenCalledTimes(1);
      expect(toggleSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { value: true }
        })
      );
    }
  });

  test("should respond to Enter key", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <div slot="title">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const header = element.shadowRoot?.querySelector(".header");
    expect(header).toBeTruthy();

    if (header) {
      header.dispatchEvent(new KeyboardEvent("keydown", { key: "Enter" }));
      await elementUpdated(element);

      expect(element.expanded).toBe(true);
    }
  });

  test("should respond to Space key", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <div slot="header">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const header = element.shadowRoot?.querySelector(".header");
    expect(header).toBeTruthy();

    if (header) {
      header.dispatchEvent(new KeyboardEvent("keydown", { key: " " }));
      await elementUpdated(element);

      expect(element.expanded).toBe(true);
    }
  });

  test("should have proper accessibility attributes", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel>
        <div slot="header">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const header = element.shadowRoot?.querySelector(".header");

    expect(header?.getAttribute("role")).toBe("button");
    expect(header?.getAttribute("aria-expanded")).toBe("false");
    expect(header?.getAttribute("tabindex")).toBe("0");
  });

  test("should update accessibility attributes when expanded", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel expanded>
        <div slot="header">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const header = element.shadowRoot?.querySelector(".header");

    expect(header?.getAttribute("aria-expanded")).toBe("true");
  });

  test("should apply dark mode theme", async () => {
    const element: HTMLElementTagNameMap["snf-expansion-panel"] = await fixture(html`
      <snf-expansion-panel theme="dark">
        <div slot="header">Panel Header</div>
        <div>Panel Content</div>
      </snf-expansion-panel>
    `);

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const header = element.shadowRoot?.querySelector(".header");

    expect(header?.classList.contains("dark-mode")).toBe(true);
  });
});
