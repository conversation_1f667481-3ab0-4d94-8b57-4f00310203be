import ExpansionPanel from "./expansion-panel.component";
import docs from './expansion-panel.md?raw';
import { html } from 'lit-html';
import { Meta, StoryFn, StoryObj, WebComponentsRenderer } from '@storybook/web-components';
import { unsafeHTML } from 'lit-html/directives/unsafe-html.js';
import { withActions } from '@storybook/addon-actions/decorator';
import "./expansion-panel.component";
import "../badges/statusbadge/status-badge.component";
import "../buttons/link-button/link-button.component";
import "../icon/icon.component";

const meta: Meta<ExpansionPanel> = {
  title: 'Components/Expansion Panel',
  component: 'snf-expansion-panel',
  argTypes: {
    expanded: { control: 'boolean' },
    hasError: { control: 'boolean' },
    disabled: { control: 'boolean' },
    theme: { control: 'select', options: ['light', 'dark'] },
  },
  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
    actions: {
      handles: ['toggle'],
    },
  },
  decorators: [withActions<WebComponentsRenderer>],
};
export default meta;

const Template: StoryFn<ExpansionPanel> = ({
  expanded,
  hasError,
  disabled,
  theme,
  slot
}) => html`
  <snf-expansion-panel
    ?expanded=${expanded}
    ?hasError=${hasError}
    ?disabled=${disabled}
    theme=${theme}
  >
    <span slot="title">Title</span>
    ${unsafeHTML(slot)}
  </snf-expansion-panel>
`;

export const Default: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    slot: '<p>Configure your general application settings here. You can modify preferences, update your profile information, and manage notification settings.</p>'
  },
};

export const Expanded: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    expanded: true,
    slot: '<p>Your account details and personal information are displayed here. You can update your email, password, and other account settings.</p><p>Last updated: January 15, 2024</p>'
  },
};

export const WithSubTitle: StoryObj<ExpansionPanel> = {
  render: () => html`
      <snf-expansion-panel>
        <span slot="sub-title">This is a sub title</span>
        <p>Manage your personal details including name, email, and contact information.</p>
      </snf-expansion-panel>
  `,
};

export const Disabled: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    disabled: true,
    slot: '<p>Configure your general application settings here. You can modify preferences, update your profile information, and manage notification settings.</p>'
  },
};

export const WithError: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    expanded: true,
    hasError: true,
    slot: '<p>Your account details and personal information are displayed here. You can update your email, password, and other account settings.</p><p>Last updated: January 15, 2024</p>'
  },
};

export const WithActionIcon: StoryObj<ExpansionPanel> = {
  render: () => html`
      <snf-expansion-panel>
        <span slot="title">Main Title</span>
        <div slot="action-items">
          <snf-link-button>
            <snf-icon slot="prefix" iconStyle="outlined" iconsize="small">edit</snf-icon>
            <span>Edit</span>
          </snf-link-button>
          <snf-link-button>
            <snf-icon slot="prefix" iconStyle="outlined" iconsize="small">edit</snf-icon>
            <span>Edit</span>
          </snf-link-button>
        </div>
        <p>Manage your personal details including name, email, and contact information.</p>
      </snf-expansion-panel>
  `,
};

export const WithTitleAndSubContentContent: StoryObj<ExpansionPanel> = {
  render: () => html`
      <snf-expansion-panel>
        <span slot="title">Main Title</span>
        <span slot="title-content" style="display: flex; gap: 8px; align-items: center;">
            <snf-status-badge
              color="blue"
            >
            Whatever
          </snf-status-badge>
          <snf-status-badge
            color="yellow"
          >
            Whatever
          </snf-status-badge>
          <snf-status-badge
            color="red"
          >
            Whatever
          </snf-status-badge>
        </span>
        <span slot="sub-title">Sub content of the header, with a loooong text</span>
        <p>Manage your personal details including name, email, and contact information.</p>
      </snf-expansion-panel>
  `,
};

export const WithDarkTheme: StoryObj<ExpansionPanel> = {
  render: () => html`
      <snf-expansion-panel theme="dark">
        <span slot="title">Main Title</span>
        <span slot="title-content">
          Some content on the right side of the title
        </span>
        <span slot="sub-title">Sub content of the header, with a loooong text</span>
        <p>Manage your personal details including name, email, and contact information.</p>
      </snf-expansion-panel>
  `,
};

export const WithALotOfContentInHeader: StoryObj<ExpansionPanel> = {
  render: () => html`
      <snf-expansion-panel>
        <span slot="title">Main Title that can be suuuuuper long and boring, but we have to test it</span>
        <span slot="title-content" style="display: flex; gap: 8px; align-items: center;">
            <snf-status-badge
              color="blue"
            >
            Whatever
          </snf-status-badge>
          <snf-status-badge
            color="yellow"
          >
            Whatever
          </snf-status-badge>
          <snf-status-badge
            color="red"
          >
            Whatever
          </snf-status-badge>
                    <snf-status-badge
                      color="red"
                    >
            Whatever
          </snf-status-badge>
                    <snf-status-badge
                      color="red"
                    >
            Whatever
          </snf-status-badge>
        </span>
        <span slot="sub-title">Sub content of the header, with a loooong text and much more and more that will never end because we have to test it</span>
        <p>Manage your personal details including name, email, and contact information.</p>
      </snf-expansion-panel>
  `,
};

export const WithRichContent: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    slot: `
      <div style="display: flex; flex-direction: column; gap: 12px;">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
          <div style="margin-top: 8px;">
            <strong>Version:</strong> 2.1.4
          </div>
          <div style="margin-top: 8px;">
            <strong>Build:</strong> 20240115-1234
          </div>
          <div>
            <strong>Environment:</strong> Production
          </div>
          <div>
            <strong>Region:</strong> Europe West
          </div>
        </div>
        <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 8px 0;">
        <div>
          <strong>Status:</strong> 
          <span style="color: #4caf50; font-weight: 600;">All systems operational</span>
        </div>
      </div>
    `
  },
};

export const MultipleExpansionPanels: StoryObj = {
  render: () => html`
    <div style="display: flex; flex-direction: column; gap: 16px; max-width: 600px;">
      <snf-expansion-panel>
        <div slot="title">Personal Information</div>
        <p>Manage your personal details including name, email, and contact information.</p>
      </snf-expansion-panel>
      
      <snf-expansion-panel expanded>
        <div slot="title">Security & Privacy</div>
        <p>Configure your security settings, manage two-factor authentication, and review privacy preferences.</p>
        <p>Last security check: 2 days ago</p>
      </snf-expansion-panel>
      
      <snf-expansion-panel>
        <div slot="title">Notifications</div>
        <p>Choose how and when you want to receive notifications from the application.</p>
      </snf-expansion-panel>
      
      <snf-expansion-panel disabled>
        <div slot="title">Beta Features</div>
        <p>Access experimental features and provide feedback to help improve the application.</p>
      </snf-expansion-panel>
    </div>
  `,
};
