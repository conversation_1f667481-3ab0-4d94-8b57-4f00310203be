@use "@styles/style" as snf;

:host {
  display: block;

  .flex-item {
    text-align: center;
    align-items: center;
  }

  .toggle-switch {
    display: inline-grid; grid-auto-flow: column; grid-auto-columns: 1fr;
    background-color: var(--color-scheme-surface-container);
    align-items: center;
    gap: snf.spacing(1);
    border-radius: 40px;
    padding: snf.spacing(1);
    width: fit-content;
    cursor: pointer;
    
    span {
      padding: 0 snf.spacing(2);
    }

    &-not-selected {
      cursor: pointer;
      pointer-events: auto;
      font-size: var(--typescale-label-medium-size);
      border: hidden;
      width: 100%;
      height: snf.spacing(4);

      &:hover {
        background-color: snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus);

        box-shadow: 0 0 0 snf.spacing(1) snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
      }
    }

    &-showLine {
      border-left: var(--stroke-small) solid var(--color-scheme-white);
      padding-left: snf.spacing(1);
    }

    &-hideLine {
      border-left: var(--stroke-small) solid var(--color-scheme-surface-container);;
      padding-left: snf.spacing(1);
    }

    &-button {
      height: snf.spacing(4);
      border: hidden;
      font-size: var(--typescale-label-medium-size);
      width: 100%;
      white-space: nowrap;

      &:hover {
        background-color: snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus);
        box-shadow: 0 0 0 snf.spacing(1) snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
        cursor: pointer;
      }
    }

    &.toggle-on {
      background-color: snf.color-scheme(success);
      color: snf.color-scheme(on-success);

      &.disabled {
        background-color: snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
      }
    }

    &.toggle-off {
      background-color: snf.color-scheme(error);
      color: snf.color-scheme(on-error);

      &.disabled {
        background-color: snf.hex-opacity(
            snf.$color-scheme-on-surface,
            snf.$state-layer-focus
        );
      }
    }

    &.disabled {
      color: snf.hex-opacity(
          snf.$color-scheme-on-surface,
          snf.$state-layer-disable
      );
      cursor: none !important;
      pointer-events: none;
    }
  }
}
