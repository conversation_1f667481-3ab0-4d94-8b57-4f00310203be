import { Meta, StoryFn, WebComponentsRenderer } from "@storybook/web-components";

import "./text-toggle-switch.component";
import docs from "./text-toggle-switch.md?raw";
import { html } from "lit-html";
import { withActions } from "@storybook/addon-actions/decorator";
import { ifDefined } from "lit-html/directives/if-defined.js";
import TextToggleSwitch from "./text-toggle-switch.component";
import { toggleValues } from "../../internals/utils/toggle-value";


const meta: Meta<TextToggleSwitch> = {
  title: "Components/Text Toggle Switch",
  component: "snf-text-toggle-switch",
  argTypes: {
    toggleValue: { control: 'select', options: toggleValues },
    disabled: { control: 'boolean' },
    labelOff: { control: 'text' },
    labelOn: { control: 'text' },
  },

  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
  },

  decorators: [withActions<WebComponentsRenderer>],
}
export default meta;

export const Default: StoryFn<TextToggleSwitch> =
  ({ labelOn, labelOff, disabled, toggleValue }) =>
    html`
      <snf-text-toggle-switch
        labelOn=${(labelOn ?? 'Accept')}
        labelOff=${(labelOff ?? 'Reject')}
        ?disabled=${(disabled)}
        toggleValue=${ifDefined(toggleValue)}
      ></snf-text-toggle-switch>`;




