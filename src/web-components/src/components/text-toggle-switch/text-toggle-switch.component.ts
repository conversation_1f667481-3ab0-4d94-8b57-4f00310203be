import BaseElement from "../../internals/baseElement/baseElement";
import { html, unsafeCSS } from "lit";
import { property } from "lit/decorators.js";
import '../icon/icon.component';
import styles from './text-toggle-switch.scss?inline';
import registerElement from "../registerElement";
import { ToggleValue } from "../../internals/utils/toggle-value";

/**
 * @toggleValue - Value of the toggle switch (default: none), on, off
 * @disabled Whether the button should be disabled (default: false)
 * @labelOff Label for the off state (default: '')
 * @labelOn Label for the on state (default: '')
 * @toggleOn Event emitted when the toggle switch is clicked
 */
export default class TextToggleSwitch extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({ type: String })
  public toggleValue: ToggleValue = 'none';

  @property({ type: Boolean })
  public disabled = false;

  @property({ type: String })
  public labelOff = '';

  @property({ type: String })
  public labelOn = '';

  override render() {
    return html`
      <div class="toggle-switch  ${this.disabled ? 'disabled' : ''}">
        <div class="flex-item">
          ${this.showToggleOffIcon() ? html`
              <button class="toggle-switch-button toggle-switch ${this.toggleStyleClassSelected()}"
                      @click="${() => this.toggle(false)}">
                <span>
                   ${this.labelOff}
                </span>
              </button>` :
            html`
              <button class="toggle-switch-not-selected toggle-switch ${this.toggleStyleClass()}"
                      @click="${() => this.toggle(false)}">
                <span>
                   ${this.labelOff}
                </span>
              </button>`}
        </div>
        <div class="flex-item ${this.toggleValue === 'none' ? 'toggle-switch-showLine' : 'toggle-switch-hideLine'}">

          ${this.showToggleOnIcon() ? html`
              <button class="toggle-switch-button toggle-switch ${this.toggleStyleClassSelected()}"
                      @click="${() => this.toggle(true)}">
                <span>
                   ${this.labelOn}
                </span>
              </button>` :
            html`
              <button class="toggle-switch-not-selected toggle-switch ${this.toggleStyleClass()}"
                      @click="${() => this.toggle(true)}">
                <span>
                   ${this.labelOn}
                </span>
              </button>`}
        </div>
      </div>
    `;
  }

  private toggle(value: boolean): void {
    if (this.disabled) {
      return;
    }
    this.toggleValue = value ? 'on' : 'off';

    this.dispatchEvent(new CustomEvent('toggleOn', {
      detail: { value: this.toggleValue === 'on' },
      bubbles: true,
      composed: true
    }));
  }

  private toggleStyleClassSelected(): string {
    if (this.disabled) {
      if (this.toggleValue === 'off') {
        return 'toggle-off disabled';
      }
      if (this.toggleValue === 'on') {
        return 'toggle-on disabled';
      }
      return 'disabled';
    }
    if (this.toggleValue === 'on') {
      return 'toggle-on';
    }
    if (this.toggleValue === 'off') {
      return 'toggle-off';
    }
    return 'none';
  }

  private toggleStyleClass(): string {
    if (this.disabled) {
      return 'disabled';
    }
    return '';
  }

  private showToggleOnIcon(): boolean {
    return this.toggleValue !== 'off';
  };

  private showToggleOffIcon(): boolean {
    return this.toggleValue !== 'on';
  };

}

registerElement('snf-text-toggle-switch', TextToggleSwitch);

declare global {
  interface HTMLElementTagNameMap {
    'snf-text-toggle-switch': TextToggleSwitch;
  }
}
