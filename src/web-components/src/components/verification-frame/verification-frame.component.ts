import BaseElement from '../../internals/baseElement/baseElement';
import { html, nothing, unsafeCSS } from 'lit';
import { property, queryAssignedElements } from 'lit/decorators.js';
import '../icon/icon.component';
import styles from './verification-frame.scss?inline';
import '../icon-toggle-switch/icon-toggle-switch.component';
import '../text-toggle-switch/text-toggle-switch.component';
import registerElement from "../registerElement";
import { ToggleValue } from "../../internals/utils/toggle-value";

export const frameColor = [
  'red',
  'blue',
  'green',
  'yellow',
  'none'
] as const;
export type FrameColor = typeof frameColor[number];

export const toggleType = [
  'icon',
  'text',
  'none'
] as const;
export type ToggleType = typeof toggleType[number];


/**
 * @frameColor - color of the frame
 * @toggleType - type of the toggle switch
 * @toggleLabelOn - label for the toggle switch (type=text) when it is on
 * @toggleLabelOff - label for the toggle switch (type=text) when it is off
 * @toggleValue - value of the toggle switch
 * @isToggleDisabled - disable the toggle switch
 * @toggleOn Event emitted when the toggle switch is clicked
 * @slot content to validate
 * @slot footer - slot for content on the right side of the toggle switch
 */
export default class VerificationFrame extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({ type: String })
  public frameColor: FrameColor = 'none';

  @property({ type: String })
  public toggleType: ToggleType = 'none';

  @property({ type: String })
  public toggleLabelOn = 'On';

  @property({ type: String })
  public toggleLabelOff = 'Off';

  @property({ type: String })
  public toggleValue: ToggleValue = 'none';

  @property({ type: Boolean })
  public isToggleDisabled: boolean = false;

  @queryAssignedElements({ slot: 'footer' }) footerSlot!: Array<HTMLElement>;

  override render() {
    return html`
      <div class='verification-frame ${this.frameColor}'>
        <!-- Content Slot -->
        <slot></slot>

        <!-- Footer Section -->
        <div class='footer'>
          ${this.renderToggle()}
          <div class='footer-content'>
            <slot name="footer"></slot>
          </div>
        </div>
      </div>`;
  }


  renderToggle() {
    switch (this.toggleType) {
      case 'icon':
        return this.getIconToggleSwitch();
      case 'text':
        return this.getTextToggleSwitch();
      case 'none':
      default:  
        return nothing;
    }
  }

  getIconToggleSwitch() {
    return html`
      <snf-icon-toggle-switch
        toggleValue='${this.toggleValue}'
        ?disabled=${this.isToggleDisabled}
        @toggleOn="${this.handleToggle}"
      >
      </snf-icon-toggle-switch>`;
  }


  getTextToggleSwitch() {
    return html`
      <snf-text-toggle-switch
        labelon='${this.toggleLabelOn}'
        @toggleOn="${this.handleToggle}"
        labeloff='${this.toggleLabelOff}'
        toggleValue='${this.toggleValue}'
        ?disabled=${this.isToggleDisabled}>
      </snf-text-toggle-switch>`;
  }

  private handleToggle(event: CustomEvent) {
    event.stopPropagation();
    this.toggleValue = event.detail.value ? 'on' : 'off';
    this.dispatchEvent(new CustomEvent(
      'toggleOn', {
        detail: { value: event.detail.value },
        bubbles: true,
        composed: true 
      })
    );
  }
}

registerElement('snf-verification-frame', VerificationFrame);

declare global {
  interface HTMLElementTagNameMap {
    'snf-verification-frame': VerificationFrame;
  }
}
