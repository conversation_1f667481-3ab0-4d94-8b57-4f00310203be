import "./readonly-checkbox.component";
import { describe, expect, test, vi } from "vitest";
import { elementUpdated, fixture, html } from "@open-wc/testing-helpers";

describe("ReadOnlyCheckbox", () => {
  test("should display a checked check box icon and label", async () => {
    const isSelected: boolean = true;
    const element: HTMLElementTagNameMap["snf-readonly-checkbox"] = await fixture(
      html` <snf-readonly-checkbox isSelected="${isSelected === true}">test label</snf-readonly-checkbox> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const containerElement = element.shadowRoot?.querySelector("div");
    expect(containerElement).not.toBeNull();
    expect(containerElement!.querySelector("span")?.className).toContain(
      "material-icons",
    );
    expect(containerElement!.querySelector("span")?.textContent).toContain(
      "check_box",
    );
    expect(element.shadowRoot
      ?.querySelector("slot")
      ?.assignedNodes({ flatten: true })[0].textContent,
    ).toContain("test label");
  });
  test("should display a unchecked check box icon and label", async () => {
    const element: HTMLElementTagNameMap["snf-readonly-checkbox"] = await fixture(
      html` <snf-readonly-checkbox ?isSelected="${false}">test label</snf-readonly-checkbox> `,
    );

    await elementUpdated(element);
    await vi.dynamicImportSettled();

    const containerElement = element.shadowRoot?.querySelector("div");
    expect(containerElement).not.toBeNull();
    expect(containerElement!.querySelector("span")?.className).toContain(
      "material-icons",
    );
    expect(containerElement!.querySelector("span")?.textContent).toContain(
      "check_box_outline_blank",
    );
    expect(element.shadowRoot
      ?.querySelector("slot")
      ?.assignedNodes({ flatten: true })[0].textContent,
    ).toContain("test label");
  });
});
