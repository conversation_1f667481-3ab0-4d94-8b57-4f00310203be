import { WithSubRows } from './components/table/table.component';

const currencies = ['CHF', 'EUR', 'USD'] as const;
export type Currency = typeof currencies[number];

export interface Price {
  currency: Currency;
  amount: number;
}

const relationShipStatuses = ['relationship', 'complicated', 'single'] as const;
type RelationshipStatus = typeof relationShipStatuses[number];

export interface Person extends WithSubRows<Person> {
  id?: number;
  description?: string;
  firstName: string;
  lastName: string;
  age: number | null;
  visits: number;
  progress: number;
  status: RelationshipStatus;
  createdAt: Date;
  wealth: Price;
  company: string;
}

export const simplePersonData: Person[] = [
  {
    'firstName': 'Claire',
    'lastName': 'Iten',
    'age': 23,
    'visits': 157,
    'progress': 99,
    'createdAt': new Date('2003-03-15T18:23:15.916Z'),
    'status': 'single',
    'company': 'Schoch-Wagner',
    'wealth': { 'currency': 'USD', 'amount': 762051 },
  }, {
    'firstName': 'Hans',
    'lastName': 'Betschart',
    'age': null,
    'visits': 820,
    'progress': 68,
    'createdAt': new Date('2000-11-20T03:13:59.117Z'),
    'status': 'complicated',
    'company': 'Zingg-Steinmann',
    'wealth': { 'currency': 'USD', 'amount': -131680 },
  }, {
    'firstName': 'Luca',
    'lastName': 'Hoffmann',
    'age': 25,
    'visits': 501,
    'progress': 84,
    'createdAt': new Date('1994-12-24T14:01:04.526Z'),
    'status': 'relationship',
    'company': 'Hirt-Baumgartner',
    'wealth': { 'currency': 'CHF', 'amount': 40846 },
  }, {
    'firstName': 'René',
    'lastName': 'Moser',
    'age': 36,
    'visits': 363,
    'progress': 25,
    'createdAt': new Date('2020-07-23T10:50:53.504Z'),
    'status': 'complicated',
    'company': 'Schnyder-Brun',
    'wealth': { 'currency': 'CHF', 'amount': 283283 },
  }, {
    'firstName': 'Ingrid',
    'lastName': 'Schaub',
    'age': 34,
    'visits': 212,
    'progress': 19,
    'createdAt': new Date('2016-12-03T13:42:56.937Z'),
    'status': 'complicated',
    'company': 'Ackermann, Berger und Spörri',
    'wealth': { 'currency': 'USD', 'amount': 267884 },
  }, {
    'firstName': 'Francesco',
    'lastName': 'Schneider',
    'age': null,
    'visits': 367,
    'progress': 18,
    'createdAt': new Date('2001-07-03T01:34:13.304Z'),
    'status': 'complicated',
    'company': 'Baur-Wegmann',
    'wealth': { 'currency': 'EUR', 'amount': 989051 },
  }, {
    'firstName': 'Heidi',
    'lastName': 'Meister',
    'age': 19,
    'visits': 291,
    'progress': 87,
    'createdAt': new Date('2006-05-12T07:36:01.313Z'),
    'status': 'relationship',
    'company': 'Leunberger, Albrecht und Brunner',
    'wealth': { 'currency': 'USD', 'amount': 184847 },
  }, {
    'firstName': 'Ruedi',
    'lastName': 'Amrein',
    'age': 24,
    'visits': 763,
    'progress': 70,
    'createdAt': new Date('2013-10-29T17:20:07.811Z'),
    'status': 'single',
    'company': 'Stadelmann, Lutz und Spörri',
    'wealth': { 'currency': 'USD', 'amount': 96046 },
  }, {
    'firstName': 'Hansruedi',
    'lastName': 'Eichenberger',
    'age': 4,
    'visits': 117,
    'progress': 48,
    'createdAt': new Date('2007-05-15T10:23:29.505Z'),
    'status': 'relationship',
    'company': 'Hürlimann, Zollinger und Schwab',
    'wealth': { 'currency': 'EUR', 'amount': 125880 },
  }, {
    'firstName': 'John',
    'lastName': 'Fuchs',
    'age': null,
    'visits': 98,
    'progress': 56,
    'createdAt': new Date('2017-06-28T21:02:30.895Z'),
    'status': 'relationship',
    'company': 'Schärer Gruppe',
    'wealth': { 'currency': 'EUR', 'amount': 257210 },
  }, {
    'firstName': 'Angelo',
    'lastName': 'Martin',
    'age': 33,
    'visits': 366,
    'progress': 2,
    'createdAt': new Date('2005-10-03T09:04:17.260Z'),
    'status': 'complicated',
    'company': 'Hunziker-Studer',
    'wealth': { 'currency': 'USD', 'amount': 213291 },
  }, {
    'firstName': 'Micheline',
    'lastName': 'Burri',
    'age': 36,
    'visits': 674,
    'progress': 54,
    'createdAt': new Date('2005-06-12T12:38:31.008Z'),
    'status': 'single',
    'company': 'Häfliger, Probst und Isler',
    'wealth': { 'currency': 'CHF', 'amount': 791826 },
  }, {
    'firstName': 'Massimo',
    'lastName': 'Spörri',
    'age': 14,
    'visits': 525,
    'progress': 3,
    'createdAt': new Date('2017-04-14T08:42:48.630Z'),
    'status': 'single',
    'company': 'Kälin AG',
    'wealth': { 'currency': 'CHF', 'amount': -109068 },
  }, {
    'firstName': 'Yvette',
    'lastName': 'Ritter',
    'age': 11,
    'visits': 267,
    'progress': 33,
    'createdAt': new Date('2022-10-13T00:31:35.226Z'),
    'status': 'complicated',
    'company': 'Schwarz Inc.',
    'wealth': { 'currency': 'USD', 'amount': 259734 },
  }, {
    'firstName': 'Werner',
    'lastName': 'Lehmann',
    'age': 17,
    'visits': 878,
    'progress': 66,
    'createdAt': new Date('2006-05-22T05:04:48.502Z'),
    'status': 'complicated',
    'company': 'Stauffer & Co.',
    'wealth': { 'currency': 'CHF', 'amount': 738495 },
  }, {
    'firstName': 'Werner',
    'lastName': 'Wenger',
    'age': 10,
    'visits': 940,
    'progress': 4,
    'createdAt': new Date('2002-04-30T15:02:51.812Z'),
    'status': 'relationship',
    'company': 'Michel LLC',
    'wealth': { 'currency': 'USD', 'amount': 152312 },
  }, {
    'firstName': 'Laurence',
    'lastName': 'Winkler',
    'age': 36,
    'visits': 103,
    'progress': 46,
    'createdAt': new Date('2019-09-26T12:26:46.922Z'),
    'status': 'complicated',
    'company': 'Brunner-Knecht',
    'wealth': { 'currency': 'EUR', 'amount': -153158 },
  }, {
    'firstName': 'Reto',
    'lastName': 'Wolf',
    'age': null,
    'visits': 803,
    'progress': 52,
    'createdAt': new Date('1990-08-21T02:17:57.586Z'),
    'status': 'complicated',
    'company': 'Zürcher und Partner',
    'wealth': { 'currency': 'EUR', 'amount': 768630 },
  }, {
    'firstName': 'Johannes',
    'lastName': 'Kohler',
    'age': 26,
    'visits': 393,
    'progress': 12,
    'createdAt': new Date('1991-12-29T00:34:42.042Z'),
    'status': 'complicated',
    'company': 'Bauer GmbH',
    'wealth': { 'currency': 'USD', 'amount': 300895 },
  }, {
    'firstName': 'Guido',
    'lastName': 'Hauser',
    'age': 40,
    'visits': 214,
    'progress': 51,
    'createdAt': new Date('2001-03-14T16:18:33.207Z'),
    'status': 'single',
    'company': 'Schumacher Gruppe',
    'wealth': { 'currency': 'USD', 'amount': 132610 },
  }, {
    'firstName': 'Fredy',
    'lastName': 'Maurer',
    'age': 29,
    'visits': 606,
    'progress': 99,
    'createdAt': new Date('2020-05-08T05:20:37.933Z'),
    'status': 'relationship',
    'company': 'Schärer und Söhne',
    'wealth': { 'currency': 'CHF', 'amount': -36411 },
  }, {
    'firstName': 'Johannes',
    'lastName': 'Lanz',
    'age': 29,
    'visits': 145,
    'progress': 34,
    'createdAt': new Date('2002-04-13T07:47:23.848Z'),
    'status': 'single',
    'company': 'Christen, Bianchi und Bucher',
    'wealth': { 'currency': 'CHF', 'amount': 360214 },
  }, {
    'firstName': 'Anne-Marie',
    'lastName': 'Schweizer',
    'age': 32,
    'visits': 16,
    'progress': 19,
    'createdAt': new Date('2003-12-12T00:53:16.149Z'),
    'status': 'single',
    'company': 'Fankhauser-Blaser',
    'wealth': { 'currency': 'EUR', 'amount': -154390 },
  }, {
    'firstName': 'Franco',
    'lastName': 'Sutter',
    'age': 24,
    'visits': 232,
    'progress': 97,
    'createdAt': new Date('1992-08-10T02:52:40.067Z'),
    'status': 'single',
    'company': 'Ernst-Gfeller',
    'wealth': { 'currency': 'EUR', 'amount': 409073 },
  }, {
    'firstName': 'Frank',
    'lastName': 'Ferrari',
    'age': 37,
    'visits': 507,
    'progress': 88,
    'createdAt': new Date('2017-01-07T17:24:42.697Z'),
    'status': 'relationship',
    'company': 'Arnold-Steiger',
    'wealth': { 'currency': 'EUR', 'amount': 736595 },
  }, {
    'firstName': 'Joseph',
    'lastName': 'Stutz',
    'age': 13,
    'visits': 311,
    'progress': 100,
    'createdAt': new Date('2023-01-29T20:58:24.971Z'),
    'status': 'complicated',
    'company': 'Schürch und Söhne',
    'wealth': { 'currency': 'USD', 'amount': 534895 },
  }, {
    'firstName': 'Friedrich',
    'lastName': 'Hodel',
    'age': 12,
    'visits': 822,
    'progress': 22,
    'createdAt': new Date('2002-08-21T21:38:53.300Z'),
    'status': 'complicated',
    'company': 'Arnold, Iten und Benz',
    'wealth': { 'currency': 'EUR', 'amount': 626176 },
  }, {
    'firstName': 'Susanna',
    'lastName': 'Bauer',
    'age': 31,
    'visits': 167,
    'progress': 26,
    'createdAt': new Date('2014-09-04T06:55:31.751Z'),
    'status': 'relationship',
    'company': 'Kälin, Lustenberger und Ferrari',
    'wealth': { 'currency': 'CHF', 'amount': 610058 },
  }, {
    'firstName': 'Pascal',
    'lastName': 'Jäggi',
    'age': 14,
    'visits': 504,
    'progress': 79,
    'createdAt': new Date('2004-01-15T02:42:10.829Z'),
    'status': 'single',
    'company': 'Kaufmann, Brun und Baumgartner',
    'wealth': { 'currency': 'EUR', 'amount': 548045 },
  }, {
    'firstName': 'Josiane',
    'lastName': 'Stöckli',
    'age': null,
    'visits': 815,
    'progress': 17,
    'createdAt': new Date('2006-06-08T13:38:26.440Z'),
    'status': 'relationship',
    'company': 'Egli, Bühlmann und Näf',
    'wealth': { 'currency': 'USD', 'amount': -97867 },
  }, {
    'firstName': 'Victor',
    'lastName': 'Gloor',
    'age': 26,
    'visits': 988,
    'progress': 50,
    'createdAt': new Date('2010-06-11T10:44:49.688Z'),
    'status': 'relationship',
    'company': 'Braun, Hauser und Kessler',
    'wealth': { 'currency': 'CHF', 'amount': -73844 },
  }, {
    'firstName': 'Christoph',
    'lastName': 'Studer',
    'age': 18,
    'visits': 935,
    'progress': 80,
    'createdAt': new Date('1994-08-02T09:17:53.803Z'),
    'status': 'relationship',
    'company': 'Gut-Hasler',
    'wealth': { 'currency': 'CHF', 'amount': -182958 },
  }, {
    'firstName': 'Véronique',
    'lastName': 'Betschart',
    'age': 19,
    'visits': 794,
    'progress': 68,
    'createdAt': new Date('1999-07-09T16:37:26.356Z'),
    'status': 'relationship',
    'company': 'Jost AG',
    'wealth': { 'currency': 'USD', 'amount': 32701 },
  }, {
    'firstName': 'Ernst',
    'lastName': 'Schweizer',
    'age': 11,
    'visits': 179,
    'progress': 97,
    'createdAt': new Date('1995-04-01T05:12:21.264Z'),
    'status': 'relationship',
    'company': 'Kunz GmbH',
    'wealth': { 'currency': 'CHF', 'amount': -75438 },
  }, {
    'firstName': 'Myriam',
    'lastName': 'Leu',
    'age': 37,
    'visits': 502,
    'progress': 84,
    'createdAt': new Date('2016-09-26T01:17:23.039Z'),
    'status': 'complicated',
    'company': 'Herzog, Schnyder und Braun',
    'wealth': { 'currency': 'EUR', 'amount': 52060 },
  }, {
    'firstName': 'Hans-Ulrich',
    'lastName': 'Bauer',
    'age': 20,
    'visits': 824,
    'progress': 82,
    'createdAt': new Date('2012-12-22T03:31:56.514Z'),
    'status': 'relationship',
    'company': 'Haas und Partner',
    'wealth': { 'currency': 'USD', 'amount': 811708 },
  }, {
    'firstName': 'Stephan',
    'lastName': 'Kohler',
    'age': 36,
    'visits': 420,
    'progress': 27,
    'createdAt': new Date('2000-12-10T07:56:24.538Z'),
    'status': 'complicated',
    'company': 'Thommen, Hofstetter und Stocker',
    'wealth': { 'currency': 'USD', 'amount': 725751 },
  }, {
    'firstName': 'Jolanda',
    'lastName': 'Kunz',
    'age': 15,
    'visits': 562,
    'progress': 55,
    'createdAt': new Date('2000-07-09T04:31:32.959Z'),
    'status': 'relationship',
    'company': 'Kuhn-Ott',
    'wealth': { 'currency': 'EUR', 'amount': -186209 },
  }, {
    'firstName': 'Pierre-Alain',
    'lastName': 'Eugster',
    'age': 2,
    'visits': 102,
    'progress': 32,
    'createdAt': new Date('1991-03-27T18:52:57.000Z'),
    'status': 'single',
    'company': 'Jäggi AG',
    'wealth': { 'currency': 'USD', 'amount': -15320 },
  }, {
    'firstName': 'Hanspeter',
    'lastName': 'Bosshard',
    'age': 27,
    'visits': 375,
    'progress': 18,
    'createdAt': new Date('2003-10-02T18:08:38.653Z'),
    'status': 'relationship',
    'company': 'Leu-Weber',
    'wealth': { 'currency': 'EUR', 'amount': 191621 },
  }, {
    'firstName': 'Florence',
    'lastName': 'Seiler',
    'age': 40,
    'visits': 237,
    'progress': 21,
    'createdAt': new Date('2014-01-25T06:34:54.161Z'),
    'status': 'relationship',
    'company': 'Jäggi, Schumacher und Hunziker',
    'wealth': { 'currency': 'USD', 'amount': -99014 },
  }, {
    'firstName': 'Bernhard',
    'lastName': 'Wirz',
    'age': 1,
    'visits': 341,
    'progress': 6,
    'createdAt': new Date('2021-02-14T23:47:14.699Z'),
    'status': 'single',
    'company': 'Flückiger und Partner',
    'wealth': { 'currency': 'USD', 'amount': 610078 },
  }, {
    'firstName': 'Bettina',
    'lastName': 'Zehnder',
    'age': 36,
    'visits': 873,
    'progress': 91,
    'createdAt': new Date('2004-01-19T07:16:06.991Z'),
    'status': 'relationship',
    'company': 'Bader und Söhne',
    'wealth': { 'currency': 'EUR', 'amount': 717475 },
  }, {
    'firstName': 'Georges',
    'lastName': 'Küng',
    'age': 12,
    'visits': 48,
    'progress': 30,
    'createdAt': new Date('2006-10-17T13:59:04.987Z'),
    'status': 'complicated',
    'company': 'Aebi & Co.',
    'wealth': { 'currency': 'USD', 'amount': 732415 },
  }, {
    'firstName': 'Theo',
    'lastName': 'Hafner',
    'age': 17,
    'visits': 662,
    'progress': 90,
    'createdAt': new Date('1990-10-05T01:25:17.146Z'),
    'status': 'relationship',
    'company': 'Schärer und Söhne',
    'wealth': { 'currency': 'USD', 'amount': 557222 },
  }, {
    'firstName': 'Ruedi',
    'lastName': 'Schaller',
    'age': 40,
    'visits': 557,
    'progress': 9,
    'createdAt': new Date('2012-03-13T07:40:57.813Z'),
    'status': 'relationship',
    'company': 'Scherrer, Lanz und Thommen',
    'wealth': { 'currency': 'USD', 'amount': 569482 },
  }, {
    'firstName': 'Brigitta',
    'lastName': 'Stauffer',
    'age': 27,
    'visits': 94,
    'progress': 42,
    'createdAt': new Date('2005-06-03T22:39:54.615Z'),
    'status': 'relationship',
    'company': 'Lüscher und Partner',
    'wealth': { 'currency': 'CHF', 'amount': 889520 },
  }, {
    'firstName': 'Nathalie',
    'lastName': 'Thommen',
    'age': null,
    'visits': 465,
    'progress': 38,
    'createdAt': new Date('2020-07-10T14:20:49.295Z'),
    'status': 'complicated',
    'company': 'Friedli Inc.',
    'wealth': { 'currency': 'CHF', 'amount': -16417 },
  }, {
    'firstName': 'Viktor',
    'lastName': 'Ernst',
    'age': 33,
    'visits': 732,
    'progress': 37,
    'createdAt': new Date('2010-09-04T21:04:34.285Z'),
    'status': 'complicated',
    'company': 'Felber, Bader und Schlatter',
    'wealth': { 'currency': 'CHF', 'amount': -175485 },
  }, {
    'firstName': 'Norbert',
    'lastName': 'Meyer',
    'age': 27,
    'visits': 198,
    'progress': 50,
    'createdAt': new Date('2021-01-08T07:39:54.332Z'),
    'status': 'single',
    'company': 'Schnyder, Schwarz und Meili',
    'wealth': { 'currency': 'CHF', 'amount': 741395 },
  }, {
    'firstName': 'Hans-Peter',
    'lastName': 'Egger',
    'age': 17,
    'visits': 618,
    'progress': 78,
    'createdAt': new Date('2019-05-30T17:52:35.920Z'),
    'status': 'relationship',
    'company': 'Lutz AG',
    'wealth': { 'currency': 'EUR', 'amount': -138289 },
  }, {
    'firstName': 'Walter',
    'lastName': 'Christen',
    'age': 2,
    'visits': 451,
    'progress': 26,
    'createdAt': new Date('1995-04-23T01:04:08.572Z'),
    'status': 'complicated',
    'company': 'Merz, Kohler und Stöckli',
    'wealth': { 'currency': 'CHF', 'amount': 848476 },
  }, {
    'firstName': 'Marc',
    'lastName': 'Lanz',
    'age': 20,
    'visits': 702,
    'progress': 82,
    'createdAt': new Date('2010-02-06T11:11:02.218Z'),
    'status': 'single',
    'company': 'Forster-Lehmann',
    'wealth': { 'currency': 'USD', 'amount': 980851 },
  }, {
    'firstName': 'Hanspeter',
    'lastName': 'Zimmermann',
    'age': 40,
    'visits': 663,
    'progress': 17,
    'createdAt': new Date('1998-01-01T15:47:17.204Z'),
    'status': 'single',
    'company': 'Hürlimann und Söhne',
    'wealth': { 'currency': 'CHF', 'amount': 579646 },
  }, {
    'firstName': 'Mario',
    'lastName': 'Ott',
    'age': 36,
    'visits': 303,
    'progress': 62,
    'createdAt': new Date('2020-01-28T14:11:45.279Z'),
    'status': 'complicated',
    'company': 'Maurer, Kessler und Wolf',
    'wealth': { 'currency': 'CHF', 'amount': 697838 },
  }, {
    'firstName': 'Ernst',
    'lastName': 'Meyer',
    'age': 22,
    'visits': 624,
    'progress': 77,
    'createdAt': new Date('2010-11-21T17:19:09.490Z'),
    'status': 'single',
    'company': 'Hofstetter-Eichenberger',
    'wealth': { 'currency': 'CHF', 'amount': 890320 },
  }, {
    'firstName': 'Arthur',
    'lastName': 'Schaller',
    'age': 40,
    'visits': 363,
    'progress': 60,
    'createdAt': new Date('2020-01-22T19:41:24.390Z'),
    'status': 'single',
    'company': 'Imhof Gruppe',
    'wealth': { 'currency': 'USD', 'amount': -70921 },
  }, {
    'firstName': 'Martha',
    'lastName': 'Braun',
    'age': 26,
    'visits': 830,
    'progress': 93,
    'createdAt': new Date('2020-03-10T11:14:27.962Z'),
    'status': 'relationship',
    'company': 'Bader-Benz',
    'wealth': { 'currency': 'USD', 'amount': 57291 },
  }, {
    'firstName': 'Josiane',
    'lastName': 'Bühler',
    'age': 2,
    'visits': 815,
    'progress': 33,
    'createdAt': new Date('2006-07-25T19:13:21.357Z'),
    'status': 'single',
    'company': 'Baur LLC',
    'wealth': { 'currency': 'CHF', 'amount': 255656 },
  }, {
    'firstName': 'François',
    'lastName': 'Friedli',
    'age': 14,
    'visits': 216,
    'progress': 58,
    'createdAt': new Date('2012-01-31T04:10:08.282Z'),
    'status': 'relationship',
    'company': 'Baumgartner Inc.',
    'wealth': { 'currency': 'USD', 'amount': 678198 },
  }, {
    'firstName': 'Michele',
    'lastName': 'Amrein',
    'age': 19,
    'visits': 104,
    'progress': 68,
    'createdAt': new Date('2002-09-04T06:52:34.481Z'),
    'status': 'relationship',
    'company': 'Hofstetter, Schwab und Merz',
    'wealth': { 'currency': 'CHF', 'amount': -162304 },
  }, {
    'firstName': 'Jean-Marc',
    'lastName': 'Bieri',
    'age': 8,
    'visits': 231,
    'progress': 35,
    'createdAt': new Date('2022-02-04T11:03:13.514Z'),
    'status': 'relationship',
    'company': 'Schürch Inc.',
    'wealth': { 'currency': 'USD', 'amount': 284815 },
  }, {
    'firstName': 'Johann',
    'lastName': 'Weber',
    'age': 8,
    'visits': 791,
    'progress': 44,
    'createdAt': new Date('2012-09-08T15:35:13.800Z'),
    'status': 'single',
    'company': 'Grob-Fehr',
    'wealth': { 'currency': 'CHF', 'amount': 668030 },
  }, {
    'firstName': 'Dora',
    'lastName': 'Bader',
    'age': 31,
    'visits': 894,
    'progress': 85,
    'createdAt': new Date('1998-12-25T17:40:10.639Z'),
    'status': 'complicated',
    'company': 'Weber & Co.',
    'wealth': { 'currency': 'USD', 'amount': 898335 },
  }, {
    'firstName': 'Regula',
    'lastName': 'Bernasconi',
    'age': 36,
    'visits': 911,
    'progress': 56,
    'createdAt': new Date('2003-04-24T07:10:30.608Z'),
    'status': 'single',
    'company': 'Ott, Fuchs und Sigrist',
    'wealth': { 'currency': 'CHF', 'amount': -163226 },
  }, {
    'firstName': 'Manuela',
    'lastName': 'Hess',
    'age': 11,
    'visits': 200,
    'progress': 84,
    'createdAt': new Date('1998-07-17T10:20:46.340Z'),
    'status': 'single',
    'company': 'Gloor & Co.',
    'wealth': { 'currency': 'CHF', 'amount': 857862 },
  }, {
    'firstName': 'Hans-Ulrich',
    'lastName': 'Scherrer',
    'age': 14,
    'visits': 843,
    'progress': 10,
    'createdAt': new Date('1999-11-11T18:44:27.083Z'),
    'status': 'complicated',
    'company': 'Burri, Hauser und Winkler',
    'wealth': { 'currency': 'EUR', 'amount': 434984 },
  }, {
    'firstName': 'Sandro',
    'lastName': 'Bosshard',
    'age': 10,
    'visits': 845,
    'progress': 4,
    'createdAt': new Date('2022-02-10T09:03:12.534Z'),
    'status': 'single',
    'company': 'Lüthi-Rey',
    'wealth': { 'currency': 'USD', 'amount': -113254 },
  }, {
    'firstName': 'Jean-Paul',
    'lastName': 'Graf',
    'age': 11,
    'visits': 400,
    'progress': 76,
    'createdAt': new Date('2023-01-10T10:37:58.050Z'),
    'status': 'single',
    'company': 'Erni-Roos',
    'wealth': { 'currency': 'EUR', 'amount': 352191 },
  }, {
    'firstName': 'Samuel',
    'lastName': 'Staub',
    'age': 22,
    'visits': 251,
    'progress': 10,
    'createdAt': new Date('2020-04-16T03:18:45.797Z'),
    'status': 'single',
    'company': 'Kuhn-Ernst',
    'wealth': { 'currency': 'USD', 'amount': 695588 },
  }, {
    'firstName': 'Marc',
    'lastName': 'Blaser',
    'age': 6,
    'visits': 215,
    'progress': 98,
    'createdAt': new Date('1993-05-14T17:30:39.706Z'),
    'status': 'complicated',
    'company': 'Hug-Zürcher',
    'wealth': { 'currency': 'CHF', 'amount': -69569 },
  }, {
    'firstName': 'Harry',
    'lastName': 'Widmer',
    'age': 33,
    'visits': 5,
    'progress': 61,
    'createdAt': new Date('2018-12-18T10:26:10.970Z'),
    'status': 'relationship',
    'company': 'Herzog-Geiger',
    'wealth': { 'currency': 'USD', 'amount': 309366 },
  }, {
    'firstName': 'Catherine',
    'lastName': 'Bauer',
    'age': 24,
    'visits': 927,
    'progress': 98,
    'createdAt': new Date('1992-03-29T06:32:37.853Z'),
    'status': 'single',
    'company': 'Lanz-Mayer',
    'wealth': { 'currency': 'USD', 'amount': 601528 },
  }, {
    'firstName': 'Denise',
    'lastName': 'Weber',
    'age': 6,
    'visits': 718,
    'progress': 5,
    'createdAt': new Date('1990-12-06T04:41:19.186Z'),
    'status': 'single',
    'company': 'Brun, Bieri und Knecht',
    'wealth': { 'currency': 'EUR', 'amount': 222359 },
  }, {
    'firstName': 'Eduard',
    'lastName': 'Stalder',
    'age': 17,
    'visits': 884,
    'progress': 58,
    'createdAt': new Date('1990-01-13T06:37:46.577Z'),
    'status': 'complicated',
    'company': 'Bachmann, Hotz und Stauffer',
    'wealth': { 'currency': 'CHF', 'amount': -105382 },
  }, {
    'firstName': 'Hans-Ulrich',
    'lastName': 'Fankhauser',
    'age': 22,
    'visits': 179,
    'progress': 100,
    'createdAt': new Date('1999-07-11T12:00:40.935Z'),
    'status': 'relationship',
    'company': 'Bauer-Baumgartner',
    'wealth': { 'currency': 'EUR', 'amount': 302939 },
  }, {
    'firstName': 'Jakob',
    'lastName': 'Felber',
    'age': 23,
    'visits': 411,
    'progress': 83,
    'createdAt': new Date('2019-11-02T17:02:41.632Z'),
    'status': 'complicated',
    'company': 'Schäfer-Bühler',
    'wealth': { 'currency': 'CHF', 'amount': 155887 },
  }, {
    'firstName': 'Karin',
    'lastName': 'Schmid',
    'age': null,
    'visits': 840,
    'progress': 26,
    'createdAt': new Date('2016-06-28T20:21:22.283Z'),
    'status': 'complicated',
    'company': 'Hunziker, Gasser und Schärer',
    'wealth': { 'currency': 'EUR', 'amount': 491092 },
  }, {
    'firstName': 'Iris',
    'lastName': 'Gfeller',
    'age': 31,
    'visits': 669,
    'progress': 17,
    'createdAt': new Date('2003-06-01T16:02:04.261Z'),
    'status': 'relationship',
    'company': 'Peter, Hauser und Scherrer',
    'wealth': { 'currency': 'USD', 'amount': -78823 },
  }, {
    'firstName': 'Rene',
    'lastName': 'Furrer',
    'age': 26,
    'visits': 879,
    'progress': 10,
    'createdAt': new Date('2021-03-11T04:32:04.014Z'),
    'status': 'relationship',
    'company': 'Küng-Mayer',
    'wealth': { 'currency': 'USD', 'amount': 112814 },
  }, {
    'firstName': 'Karl',
    'lastName': 'Betschart',
    'age': 30,
    'visits': 561,
    'progress': 60,
    'createdAt': new Date('1992-04-05T20:24:31.374Z'),
    'status': 'relationship',
    'company': 'Wehrli & Co.',
    'wealth': { 'currency': 'EUR', 'amount': 421975 },
  }, {
    'firstName': 'Frédéric',
    'lastName': 'Wettstein',
    'age': 6,
    'visits': 541,
    'progress': 65,
    'createdAt': new Date('2015-08-30T04:51:58.285Z'),
    'status': 'complicated',
    'company': 'Schäfer-Albrecht',
    'wealth': { 'currency': 'CHF', 'amount': 539446 },
  }, {
    'firstName': 'Marianne',
    'lastName': 'Stocker',
    'age': 14,
    'visits': 584,
    'progress': 38,
    'createdAt': new Date('2017-12-21T05:43:15.979Z'),
    'status': 'relationship',
    'company': 'Roos LLC',
    'wealth': { 'currency': 'EUR', 'amount': 23879 },
  }, {
    'firstName': 'Konrad',
    'lastName': 'Aebi',
    'age': 10,
    'visits': 179,
    'progress': 92,
    'createdAt': new Date('2012-01-10T01:19:16.764Z'),
    'status': 'relationship',
    'company': 'Bader, Stauffer und Schuler',
    'wealth': { 'currency': 'EUR', 'amount': 790406 },
  }, {
    'firstName': 'Luca',
    'lastName': 'Ritter',
    'age': 5,
    'visits': 932,
    'progress': 46,
    'createdAt': new Date('1995-02-20T13:37:15.700Z'),
    'status': 'relationship',
    'company': 'Wegmann, Imhof und Zollinger',
    'wealth': { 'currency': 'USD', 'amount': 321513 },
  }, {
    'firstName': 'Stéphane',
    'lastName': 'Walder',
    'age': 12,
    'visits': 414,
    'progress': 97,
    'createdAt': new Date('2011-09-16T08:28:20.966Z'),
    'status': 'single',
    'company': 'Eugster-Pfister',
    'wealth': { 'currency': 'USD', 'amount': 720163 },
  }, {
    'firstName': 'Pius',
    'lastName': 'Näf',
    'age': 31,
    'visits': 204,
    'progress': 77,
    'createdAt': new Date('2008-09-06T10:58:34.688Z'),
    'status': 'relationship',
    'company': 'Hasler, Hasler und Hotz',
    'wealth': { 'currency': 'USD', 'amount': -142540 },
  }, {
    'firstName': 'Renata',
    'lastName': 'Schwab',
    'age': 30,
    'visits': 695,
    'progress': 48,
    'createdAt': new Date('1991-05-03T08:30:42.134Z'),
    'status': 'single',
    'company': 'Studer Inc.',
    'wealth': { 'currency': 'USD', 'amount': 949545 },
  }, {
    'firstName': 'Gottfried',
    'lastName': 'Ernst',
    'age': 26,
    'visits': 635,
    'progress': 58,
    'createdAt': new Date('1999-10-24T23:21:48.119Z'),
    'status': 'single',
    'company': 'Sutter-Erni',
    'wealth': { 'currency': 'USD', 'amount': 714483 },
  }, {
    'firstName': 'Max',
    'lastName': 'Steiger',
    'age': 26,
    'visits': 840,
    'progress': 1,
    'createdAt': new Date('1999-02-12T18:14:04.484Z'),
    'status': 'complicated',
    'company': 'Wenger, Peter und Friedli',
    'wealth': { 'currency': 'EUR', 'amount': 111395 },
  }, {
    'firstName': 'Jean-Luc',
    'lastName': 'Küng',
    'age': 2,
    'visits': 847,
    'progress': 1,
    'createdAt': new Date('2007-08-04T17:03:06.042Z'),
    'status': 'single',
    'company': 'Wagner, Steffen und Stocker',
    'wealth': { 'currency': 'USD', 'amount': 362824 },
  }, {
    'firstName': 'Josette',
    'lastName': 'Beck',
    'age': 32,
    'visits': 643,
    'progress': 29,
    'createdAt': new Date('2002-02-10T06:10:59.185Z'),
    'status': 'relationship',
    'company': 'Zürcher-Müller',
    'wealth': { 'currency': 'CHF', 'amount': 435155 },
  }, {
    'firstName': 'Denis',
    'lastName': 'Thommen',
    'age': 29,
    'visits': 815,
    'progress': 86,
    'createdAt': new Date('1992-04-07T20:04:47.575Z'),
    'status': 'complicated',
    'company': 'Portmann LLC',
    'wealth': { 'currency': 'EUR', 'amount': 830493 },
  }, {
    'firstName': 'Martin',
    'lastName': 'Bühler',
    'age': 19,
    'visits': 82,
    'progress': 95,
    'createdAt': new Date('1990-08-11T02:22:52.653Z'),
    'status': 'complicated',
    'company': 'Arnold AG',
    'wealth': { 'currency': 'EUR', 'amount': -101132 },
  }, {
    'firstName': 'Françoise',
    'lastName': 'Schnyder',
    'age': 30,
    'visits': 803,
    'progress': 24,
    'createdAt': new Date('2012-03-20T07:14:50.068Z'),
    'status': 'relationship',
    'company': 'Fankhauser und Partner',
    'wealth': { 'currency': 'USD', 'amount': 508616 },
  }, {
    'firstName': 'Jean',
    'lastName': 'Graf',
    'age': 27,
    'visits': 963,
    'progress': 51,
    'createdAt': new Date('1994-07-22T20:01:21.947Z'),
    'status': 'complicated',
    'company': 'Jost-Christen',
    'wealth': { 'currency': 'CHF', 'amount': 193934 },
  }, {
    'firstName': 'Claudine',
    'lastName': 'Martin',
    'age': 8,
    'visits': 462,
    'progress': 86,
    'createdAt': new Date('2006-06-16T23:25:20.762Z'),
    'status': 'complicated',
    'company': 'Eichenberger AG',
    'wealth': { 'currency': 'EUR', 'amount': 150918 },
  }, {
    'firstName': 'Heinrich',
    'lastName': 'Schaub',
    'age': 37,
    'visits': 438,
    'progress': 7,
    'createdAt': new Date('1999-11-06T13:37:41.983Z'),
    'status': 'single',
    'company': 'Müller-Baumann',
    'wealth': { 'currency': 'EUR', 'amount': 24978 },
  }, {
    'firstName': 'Ingrid',
    'lastName': 'Meili',
    'age': null,
    'visits': 49,
    'progress': 45,
    'createdAt': new Date('1994-07-14T06:57:27.485Z'),
    'status': 'relationship',
    'company': 'Eugster und Söhne',
    'wealth': { 'currency': 'CHF', 'amount': 449357 },
  }, {
    'firstName': 'Stéphanie',
    'lastName': 'Graf',
    'age': 29,
    'visits': 812,
    'progress': 99,
    'createdAt': new Date('2023-08-31T07:51:36.086Z'),
    'status': 'complicated',
    'company': 'Hotz, Seiler und Bernasconi',
    'wealth': { 'currency': 'CHF', 'amount': 89243 },
  },
];

export const expandablePersonData: Person[] = [
  {
    'firstName': 'Fredy',
    'lastName': 'Egger',
    'age': 38,
    'visits': 482,
    'progress': 17,
    'createdAt': new Date('2009-03-13T13:39:19.174Z'),
    'status': 'single',
    'company': 'Widmer, Roos und Knecht',
    'wealth': {
      'currency': 'USD',
      'amount': 309564,
    },
    'subRows': [
      {
        'firstName': 'Bruno',
        'lastName': 'Meili',
        'age': 32,
        'visits': 565,
        'progress': 39,
        'createdAt': new Date('2023-09-03T22:54:55.394Z'),
        'status': 'relationship',
        'company': 'Schmid Inc.',
        'wealth': {
          'currency': 'USD',
          'amount': 664679,
        },
        'subRows': [
          {
            'firstName': 'Henri',
            'lastName': 'Steiger',
            'age': 31,
            'visits': 252,
            'progress': 1,
            'createdAt': new Date('2005-04-24T00:12:44.963Z'),
            'status': 'complicated',
            'company': 'Küng, Stutz und Tobler',
            'wealth': {
              'currency': 'USD',
              'amount': 527660,
            },
          },
          {
            'firstName': 'Barbara',
            'lastName': 'Leunberger',
            'age': 1,
            'visits': 784,
            'progress': 25,
            'createdAt': new Date('2009-04-09T08:52:51.159Z'),
            'status': 'complicated',
            'company': 'Stalder-Koch',
            'wealth': {
              'currency': 'USD',
              'amount': -70419,
            },
          },
        ],
      },
      {
        'firstName': 'Eduard',
        'lastName': 'Steiger',
        'age': 39,
        'visits': 3,
        'progress': 92,
        'createdAt': new Date('1992-03-10T23:44:25.492Z'),
        'status': 'relationship',
        'company': 'Walter Gruppe',
        'wealth': {
          'currency': 'CHF',
          'amount': -39540,
        },
        'subRows': [
          {
            'firstName': 'Anne',
            'lastName': 'Stucki',
            'age': 25,
            'visits': 538,
            'progress': 30,
            'createdAt': new Date('2019-08-27T21:15:51.283Z'),
            'status': 'relationship',
            'company': 'Stucki, Herzog und Berger',
            'wealth': {
              'currency': 'CHF',
              'amount': 66296,
            },
          },
          {
            'firstName': 'Myriam',
            'lastName': 'Vogt',
            'age': 27,
            'visits': 975,
            'progress': 44,
            'createdAt': new Date('1991-09-11T06:56:23.873Z'),
            'status': 'relationship',
            'company': 'Schaub, Schärer und Koch',
            'wealth': {
              'currency': 'CHF',
              'amount': 860765,
            },
          },
        ],
      },
      {
        'firstName': 'Francesco',
        'lastName': 'Schär',
        'age': 13,
        'visits': 208,
        'progress': 76,
        'createdAt': new Date('2014-05-03T11:31:46.822Z'),
        'status': 'relationship',
        'company': 'Frick-Betschart',
        'wealth': {
          'currency': 'CHF',
          'amount': 495144,
        },
        'subRows': [
          {
            'firstName': 'Daniela',
            'lastName': 'Zürcher',
            'age': 24,
            'visits': 365,
            'progress': 63,
            'createdAt': new Date('2018-02-23T18:15:00.064Z'),
            'status': 'complicated',
            'company': 'Schweizer LLC',
            'wealth': {
              'currency': 'EUR',
              'amount': 294446,
            },
          },
          {
            'firstName': 'Eric',
            'lastName': 'Forster',
            'age': 14,
            'visits': 753,
            'progress': 16,
            'createdAt': new Date('2022-01-02T05:25:58.541Z'),
            'status': 'single',
            'company': 'Peter-Stucki',
            'wealth': {
              'currency': 'CHF',
              'amount': 905855,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Claudine',
    'lastName': 'Rüegg',
    'age': 5,
    'visits': 431,
    'progress': 37,
    'createdAt': new Date('2019-06-30T09:05:46.778Z'),
    'status': 'single',
    'company': 'Winkler Inc.',
    'wealth': {
      'currency': 'CHF',
      'amount': 441339,
    },
    'subRows': [
      {
        'firstName': 'Gérard',
        'lastName': 'Zürcher',
        'age': 15,
        'visits': 841,
        'progress': 26,
        'createdAt': new Date('2009-12-06T14:22:48.213Z'),
        'status': 'single',
        'company': 'Lang und Partner',
        'wealth': {
          'currency': 'EUR',
          'amount': 908242,
        },
        'subRows': [
          {
            'firstName': 'Ulrich',
            'lastName': 'Zingg',
            'age': 3,
            'visits': 413,
            'progress': 79,
            'createdAt': new Date('2015-11-09T08:52:06.349Z'),
            'status': 'relationship',
            'company': 'Giger Inc.',
            'wealth': {
              'currency': 'EUR',
              'amount': -195705,
            },
          },
          {
            'firstName': 'Petra',
            'lastName': 'Vogt',
            'age': 9,
            'visits': 319,
            'progress': 77,
            'createdAt': new Date('2003-03-10T03:33:51.844Z'),
            'status': 'single',
            'company': 'Mettler Inc.',
            'wealth': {
              'currency': 'EUR',
              'amount': -139612,
            },
          },
        ],
      },
      {
        'firstName': 'Niklaus',
        'lastName': 'Hess',
        'age': 26,
        'visits': 714,
        'progress': 24,
        'createdAt': new Date('2006-08-19T08:57:03.150Z'),
        'status': 'complicated',
        'company': 'Leu, Stettler und Graf',
        'wealth': {
          'currency': 'EUR',
          'amount': 18525,
        },
        'subRows': [
          {
            'firstName': 'Christiane',
            'lastName': 'Senn',
            'age': 2,
            'visits': 938,
            'progress': 4,
            'createdAt': new Date('1991-11-23T22:16:50.607Z'),
            'status': 'complicated',
            'company': 'Schär Inc.',
            'wealth': {
              'currency': 'CHF',
              'amount': -163129,
            },
          },
          {
            'firstName': 'Nadia',
            'lastName': 'Amrein',
            'age': 24,
            'visits': 706,
            'progress': 80,
            'createdAt': new Date('2000-06-27T14:06:02.040Z'),
            'status': 'relationship',
            'company': 'Lutz, Fankhauser und Betschart',
            'wealth': {
              'currency': 'CHF',
              'amount': 800816,
            },
          },
        ],
      },
      {
        'firstName': 'Thomas',
        'lastName': 'Giger',
        'age': 35,
        'visits': 748,
        'progress': 0,
        'createdAt': new Date('2006-05-22T00:34:44.623Z'),
        'status': 'complicated',
        'company': 'Hofstetter & Co.',
        'wealth': {
          'currency': 'USD',
          'amount': -10964,
        },
        'subRows': [
          {
            'firstName': 'Marlies',
            'lastName': 'Kuhn',
            'age': 32,
            'visits': 345,
            'progress': 43,
            'createdAt': new Date('2009-04-24T21:34:36.372Z'),
            'status': 'relationship',
            'company': 'Flückiger-Fehr',
            'wealth': {
              'currency': 'CHF',
              'amount': 304488,
            },
          },
          {
            'firstName': 'Stephan',
            'lastName': 'Felber',
            'age': 33,
            'visits': 104,
            'progress': 26,
            'createdAt': new Date('2020-08-15T10:43:42.016Z'),
            'status': 'complicated',
            'company': 'Jenni-Felber',
            'wealth': {
              'currency': 'EUR',
              'amount': 642028,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Markus',
    'lastName': 'Leu',
    'age': 21,
    'visits': 106,
    'progress': 67,
    'createdAt': new Date('2014-03-06T07:12:27.109Z'),
    'status': 'relationship',
    'company': 'Jenni-Beck',
    'wealth': {
      'currency': 'CHF',
      'amount': 350412,
    },
    'subRows': [
      {
        'firstName': 'Bernhard',
        'lastName': 'Grob',
        'age': 37,
        'visits': 621,
        'progress': 93,
        'createdAt': new Date('2006-05-07T04:05:37.406Z'),
        'status': 'single',
        'company': 'Wenger AG',
        'wealth': {
          'currency': 'CHF',
          'amount': 859063,
        },
        'subRows': [
          {
            'firstName': 'Emil',
            'lastName': 'Weibel',
            'age': 8,
            'visits': 38,
            'progress': 55,
            'createdAt': new Date('2005-05-20T03:50:02.674Z'),
            'status': 'relationship',
            'company': 'Arnold, Bär und Leunberger',
            'wealth': {
              'currency': 'CHF',
              'amount': 921264,
            },
          },
          {
            'firstName': 'Marlies',
            'lastName': 'Marti',
            'age': 21,
            'visits': 587,
            'progress': 20,
            'createdAt': new Date('2013-09-28T23:34:50.755Z'),
            'status': 'complicated',
            'company': 'Wolf Inc.',
            'wealth': {
              'currency': 'USD',
              'amount': 296818,
            },
          },
        ],
      },
      {
        'firstName': 'Daniela',
        'lastName': 'Friedli',
        'age': 15,
        'visits': 1,
        'progress': 60,
        'createdAt': new Date('1997-11-20T03:12:16.318Z'),
        'status': 'relationship',
        'company': 'Weibel AG',
        'wealth': {
          'currency': 'EUR',
          'amount': 372279,
        },
        'subRows': [
          {
            'firstName': 'Eduard',
            'lastName': 'Schneider',
            'age': 27,
            'visits': 844,
            'progress': 44,
            'createdAt': new Date('1994-02-07T22:38:59.825Z'),
            'status': 'relationship',
            'company': 'Hofmann, Mäder und Mayer',
            'wealth': {
              'currency': 'EUR',
              'amount': 426199,
            },
          },
          {
            'firstName': 'Paolo',
            'lastName': 'Zollinger',
            'age': 22,
            'visits': 819,
            'progress': 86,
            'createdAt': new Date('2018-05-20T01:39:12.948Z'),
            'status': 'complicated',
            'company': 'Ziegler, Frei und Scheidegger',
            'wealth': {
              'currency': 'EUR',
              'amount': 624504,
            },
          },
        ],
      },
      {
        'firstName': 'Simone',
        'lastName': 'Graf',
        'age': 23,
        'visits': 671,
        'progress': 27,
        'createdAt': new Date('2020-07-05T02:09:14.990Z'),
        'status': 'complicated',
        'company': 'Schuler & Co.',
        'wealth': {
          'currency': 'USD',
          'amount': 12660,
        },
        'subRows': [
          {
            'firstName': 'Françoise',
            'lastName': 'Lüthi',
            'age': 28,
            'visits': 732,
            'progress': 14,
            'createdAt': new Date('1998-02-04T05:30:59.730Z'),
            'status': 'complicated',
            'company': 'Schweizer, Schenk und Lustenberger',
            'wealth': {
              'currency': 'EUR',
              'amount': 781598,
            },
          },
          {
            'firstName': 'Toni',
            'lastName': 'Widmer',
            'age': 31,
            'visits': 106,
            'progress': 99,
            'createdAt': new Date('2005-03-01T06:44:51.690Z'),
            'status': 'relationship',
            'company': 'Jäggi und Partner',
            'wealth': {
              'currency': 'EUR',
              'amount': 841551,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Hubert',
    'lastName': 'Martin',
    'age': 13,
    'visits': 723,
    'progress': 50,
    'createdAt': new Date('1995-09-25T22:39:42.013Z'),
    'status': 'single',
    'company': 'Blum, Bolliger und Bucher',
    'wealth': {
      'currency': 'CHF',
      'amount': 686295,
    },
    'subRows': [
      {
        'firstName': 'Monica',
        'lastName': 'Bühler',
        'age': 16,
        'visits': 723,
        'progress': 29,
        'createdAt': new Date('1998-09-18T09:25:46.727Z'),
        'status': 'relationship',
        'company': 'Kälin und Partner',
        'wealth': {
          'currency': 'EUR',
          'amount': 982953,
        },
        'subRows': [
          {
            'firstName': 'Gerhard',
            'lastName': 'Scheidegger',
            'age': 9,
            'visits': 982,
            'progress': 93,
            'createdAt': new Date('2005-02-14T11:06:06.437Z'),
            'status': 'single',
            'company': 'Näf-Wolf',
            'wealth': {
              'currency': 'USD',
              'amount': 803857,
            },
          },
          {
            'firstName': 'Jolanda',
            'lastName': 'Thommen',
            'age': 38,
            'visits': 461,
            'progress': 81,
            'createdAt': new Date('1994-10-28T10:02:08.496Z'),
            'status': 'single',
            'company': 'Schärer, Steiner und Gerber',
            'wealth': {
              'currency': 'USD',
              'amount': 30379,
            },
          },
        ],
      },
      {
        'firstName': 'Alfons',
        'lastName': 'Bär',
        'age': 29,
        'visits': 449,
        'progress': 74,
        'createdAt': new Date('2007-02-09T11:46:54.656Z'),
        'status': 'single',
        'company': 'Forster Gruppe',
        'wealth': {
          'currency': 'EUR',
          'amount': -9127,
        },
        'subRows': [
          {
            'firstName': 'Renate',
            'lastName': 'Iten',
            'age': 20,
            'visits': 423,
            'progress': 68,
            'createdAt': new Date('1999-01-01T16:13:34.734Z'),
            'status': 'single',
            'company': 'Bucher Inc.',
            'wealth': {
              'currency': 'USD',
              'amount': -155130,
            },
          },
          {
            'firstName': 'Felix',
            'lastName': 'Wagner',
            'age': 38,
            'visits': 99,
            'progress': 88,
            'createdAt': new Date('2015-11-07T00:41:59.752Z'),
            'status': 'relationship',
            'company': 'Keller, Kälin und Aebi',
            'wealth': {
              'currency': 'EUR',
              'amount': 682586,
            },
          },
        ],
      },
      {
        'firstName': 'Antonio',
        'lastName': 'Zehnder',
        'age': 10,
        'visits': 115,
        'progress': 55,
        'createdAt': new Date('1999-10-19T15:45:55.304Z'),
        'status': 'single',
        'company': 'Baur, Hunziker und Favre',
        'wealth': {
          'currency': 'USD',
          'amount': 817318,
        },
        'subRows': [
          {
            'firstName': 'Elsa',
            'lastName': 'Bättig',
            'age': 22,
            'visits': 392,
            'progress': 36,
            'createdAt': new Date('1994-11-28T17:14:56.999Z'),
            'status': 'single',
            'company': 'Schweizer, Kälin und Hürlimann',
            'wealth': {
              'currency': 'EUR',
              'amount': 329259,
            },
          },
          {
            'firstName': 'Pierre-Alain',
            'lastName': 'Mäder',
            'age': 28,
            'visits': 631,
            'progress': 15,
            'createdAt': new Date('1998-09-08T17:29:52.452Z'),
            'status': 'single',
            'company': 'Mäder und Partner',
            'wealth': {
              'currency': 'USD',
              'amount': 185171,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Silvio',
    'lastName': 'Gasser',
    'age': 5,
    'visits': 872,
    'progress': 83,
    'createdAt': new Date('2020-08-17T08:35:59.965Z'),
    'status': 'relationship',
    'company': 'Meili LLC',
    'wealth': {
      'currency': 'USD',
      'amount': -56504,
    },
    'subRows': [
      {
        'firstName': 'Madeleine',
        'lastName': 'Roth',
        'age': 36,
        'visits': 114,
        'progress': 33,
        'createdAt': new Date('1998-04-28T07:19:43.769Z'),
        'status': 'complicated',
        'company': 'Schärer und Partner',
        'wealth': {
          'currency': 'EUR',
          'amount': 366721,
        },
        'subRows': [
          {
            'firstName': 'Luigi',
            'lastName': 'Wettstein',
            'age': 34,
            'visits': 664,
            'progress': 26,
            'createdAt': new Date('2014-10-25T04:46:49.853Z'),
            'status': 'single',
            'company': 'Eichenberger Inc.',
            'wealth': {
              'currency': 'EUR',
              'amount': 503953,
            },
          },
          {
            'firstName': 'Herbert',
            'lastName': 'Kunz',
            'age': 20,
            'visits': 623,
            'progress': 19,
            'createdAt': new Date('1994-07-14T00:38:58.970Z'),
            'status': 'single',
            'company': 'Felber-Kessler',
            'wealth': {
              'currency': 'USD',
              'amount': 819728,
            },
          },
        ],
      },
      {
        'firstName': 'Niklaus',
        'lastName': 'Studer',
        'age': 38,
        'visits': 917,
        'progress': 53,
        'createdAt': new Date('2000-04-28T01:24:17.432Z'),
        'status': 'complicated',
        'company': 'Hermann GmbH',
        'wealth': {
          'currency': 'EUR',
          'amount': 919345,
        },
        'subRows': [
          {
            'firstName': 'Samuel',
            'lastName': 'Ernst',
            'age': 37,
            'visits': 582,
            'progress': 82,
            'createdAt': new Date('2008-10-09T01:09:43.887Z'),
            'status': 'single',
            'company': 'Zbinden LLC',
            'wealth': {
              'currency': 'EUR',
              'amount': 434871,
            },
          },
          {
            'firstName': 'Helena',
            'lastName': 'Grob',
            'age': 23,
            'visits': 532,
            'progress': 34,
            'createdAt': new Date('2015-05-21T15:30:00.021Z'),
            'status': 'complicated',
            'company': 'Peter Inc.',
            'wealth': {
              'currency': 'CHF',
              'amount': 485555,
            },
          },
        ],
      },
      {
        'firstName': 'Beatrix',
        'lastName': 'Eugster',
        'age': 37,
        'visits': 360,
        'progress': 13,
        'createdAt': new Date('2018-05-18T18:43:12.199Z'),
        'status': 'relationship',
        'company': 'Albrecht-Kessler',
        'wealth': {
          'currency': 'EUR',
          'amount': 143728,
        },
        'subRows': [
          {
            'firstName': 'Helen',
            'lastName': 'Lustenberger',
            'age': 36,
            'visits': 532,
            'progress': 27,
            'createdAt': new Date('2002-09-10T03:55:20.768Z'),
            'status': 'single',
            'company': 'Tobler, Hoffmann und Senn',
            'wealth': {
              'currency': 'CHF',
              'amount': 135060,
            },
          },
          {
            'firstName': 'Helen',
            'lastName': 'Steiner',
            'age': 30,
            'visits': 68,
            'progress': 47,
            'createdAt': new Date('2007-04-08T17:19:54.904Z'),
            'status': 'relationship',
            'company': 'Maier, Friedli und Wagner',
            'wealth': {
              'currency': 'CHF',
              'amount': 8638,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Fabienne',
    'lastName': 'Meier',
    'age': 26,
    'visits': 690,
    'progress': 60,
    'createdAt': new Date('2002-08-31T02:04:19.017Z'),
    'status': 'complicated',
    'company': 'Jäggi Inc.',
    'wealth': {
      'currency': 'EUR',
      'amount': 545578,
    },
    'subRows': [
      {
        'firstName': 'Michele',
        'lastName': 'Gerber',
        'age': 34,
        'visits': 675,
        'progress': 37,
        'createdAt': new Date('2004-09-28T04:30:35.894Z'),
        'status': 'relationship',
        'company': 'Hofmann und Söhne',
        'wealth': {
          'currency': 'USD',
          'amount': 119202,
        },
        'subRows': [
          {
            'firstName': 'Beatrix',
            'lastName': 'Schneider',
            'age': 12,
            'visits': 839,
            'progress': 85,
            'createdAt': new Date('2001-10-12T03:03:54.171Z'),
            'status': 'complicated',
            'company': 'Hoffmann-Steinmann',
            'wealth': {
              'currency': 'EUR',
              'amount': 241127,
            },
          },
          {
            'firstName': 'Hansueli',
            'lastName': 'Ritter',
            'age': 37,
            'visits': 522,
            'progress': 23,
            'createdAt': new Date('2018-01-15T02:38:24.567Z'),
            'status': 'complicated',
            'company': 'Wirz, Widmer und Meister',
            'wealth': {
              'currency': 'CHF',
              'amount': 791046,
            },
          },
        ],
      },
      {
        'firstName': 'Irene',
        'lastName': 'Hoffmann',
        'age': 30,
        'visits': 319,
        'progress': 20,
        'createdAt': new Date('2004-04-04T10:28:14.322Z'),
        'status': 'single',
        'company': 'Bucher, Ammann und Frey',
        'wealth': {
          'currency': 'EUR',
          'amount': -161268,
        },
        'subRows': [
          {
            'firstName': 'Ingrid',
            'lastName': 'Stucki',
            'age': 27,
            'visits': 563,
            'progress': 8,
            'createdAt': new Date('1998-05-15T13:00:11.352Z'),
            'status': 'relationship',
            'company': 'Baumgartner Gruppe',
            'wealth': {
              'currency': 'CHF',
              'amount': -48773,
            },
          },
          {
            'firstName': 'Marlène',
            'lastName': 'Meyer',
            'age': 26,
            'visits': 153,
            'progress': 55,
            'createdAt': new Date('2002-04-14T23:40:32.699Z'),
            'status': 'relationship',
            'company': 'Stocker-Wagner',
            'wealth': {
              'currency': 'EUR',
              'amount': 813598,
            },
          },
        ],
      },
      {
        'firstName': 'Frank',
        'lastName': 'Beck',
        'age': 17,
        'visits': 930,
        'progress': 8,
        'createdAt': new Date('2001-11-06T12:31:55.313Z'),
        'status': 'single',
        'company': 'Frick-Braun',
        'wealth': {
          'currency': 'CHF',
          'amount': 116890,
        },
        'subRows': [
          {
            'firstName': 'Jan',
            'lastName': 'Scheidegger',
            'age': 6,
            'visits': 795,
            'progress': 68,
            'createdAt': new Date('1997-04-02T00:25:26.528Z'),
            'status': 'single',
            'company': 'Sutter, Stöckli und Lang',
            'wealth': {
              'currency': 'CHF',
              'amount': 894632,
            },
          },
          {
            'firstName': 'Eva',
            'lastName': 'Maier',
            'age': 39,
            'visits': 937,
            'progress': 78,
            'createdAt': new Date('2013-04-26T03:27:27.105Z'),
            'status': 'single',
            'company': 'Wirz, Müller und Hunziker',
            'wealth': {
              'currency': 'EUR',
              'amount': 805745,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Angelo',
    'lastName': 'Lustenberger',
    'age': 9,
    'visits': 100,
    'progress': 2,
    'createdAt': new Date('2006-10-29T07:01:22.997Z'),
    'status': 'relationship',
    'company': 'Walder-Schoch',
    'wealth': {
      'currency': 'EUR',
      'amount': 43521,
    },
    'subRows': [
      {
        'firstName': 'Silvio',
        'lastName': 'Lutz',
        'age': 4,
        'visits': 347,
        'progress': 7,
        'createdAt': new Date('2019-03-21T16:00:48.718Z'),
        'status': 'complicated',
        'company': 'Steiger AG',
        'wealth': {
          'currency': 'EUR',
          'amount': 479104,
        },
        'subRows': [
          {
            'firstName': 'Raymond',
            'lastName': 'Schenk',
            'age': 29,
            'visits': 425,
            'progress': 33,
            'createdAt': new Date('2000-06-08T08:10:12.184Z'),
            'status': 'single',
            'company': 'Baur, Egger und Stöckli',
            'wealth': {
              'currency': 'USD',
              'amount': 353862,
            },
          },
          {
            'firstName': 'Matthias',
            'lastName': 'Huber',
            'age': 6,
            'visits': 399,
            'progress': 63,
            'createdAt': new Date('2015-07-25T20:38:00.749Z'),
            'status': 'relationship',
            'company': 'Braun, Meili und Zürcher',
            'wealth': {
              'currency': 'USD',
              'amount': -53403,
            },
          },
        ],
      },
      {
        'firstName': 'Franziska',
        'lastName': 'Furrer',
        'age': 31,
        'visits': 407,
        'progress': 25,
        'createdAt': new Date('2012-04-10T00:12:28.439Z'),
        'status': 'complicated',
        'company': 'Schoch Gruppe',
        'wealth': {
          'currency': 'CHF',
          'amount': 83672,
        },
        'subRows': [
          {
            'firstName': 'Erich',
            'lastName': 'Lüscher',
            'age': 32,
            'visits': 173,
            'progress': 4,
            'createdAt': new Date('2022-04-28T20:48:19.553Z'),
            'status': 'complicated',
            'company': 'Bühlmann, Merz und Schwab',
            'wealth': {
              'currency': 'EUR',
              'amount': 120682,
            },
          },
          {
            'firstName': 'Laurence',
            'lastName': 'Vogt',
            'age': 11,
            'visits': 317,
            'progress': 89,
            'createdAt': new Date('2008-05-03T07:59:25.786Z'),
            'status': 'relationship',
            'company': 'Jost, Amrein und Lanz',
            'wealth': {
              'currency': 'CHF',
              'amount': 454723,
            },
          },
        ],
      },
      {
        'firstName': 'Eric',
        'lastName': 'Christen',
        'age': 19,
        'visits': 832,
        'progress': 77,
        'createdAt': new Date('2015-01-27T05:29:29.063Z'),
        'status': 'relationship',
        'company': 'Schürch-Mayer',
        'wealth': {
          'currency': 'CHF',
          'amount': 334080,
        },
        'subRows': [
          {
            'firstName': 'Rene',
            'lastName': 'Senn',
            'age': 40,
            'visits': 18,
            'progress': 84,
            'createdAt': new Date('1996-07-05T12:23:26.194Z'),
            'status': 'single',
            'company': 'Blum-Näf',
            'wealth': {
              'currency': 'USD',
              'amount': 647054,
            },
          },
          {
            'firstName': 'Andreas',
            'lastName': 'Bieri',
            'age': 19,
            'visits': 270,
            'progress': 9,
            'createdAt': new Date('2005-10-26T00:00:46.161Z'),
            'status': 'relationship',
            'company': 'Merz Inc.',
            'wealth': {
              'currency': 'USD',
              'amount': 972671,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Sonja',
    'lastName': 'Stalder',
    'age': 34,
    'visits': 982,
    'progress': 72,
    'createdAt': new Date('2012-11-07T06:03:31.942Z'),
    'status': 'single',
    'company': 'Stucki, Lanz und Küng',
    'wealth': {
      'currency': 'EUR',
      'amount': -51415,
    },
    'subRows': [
      {
        'firstName': 'Christina',
        'lastName': 'Spörri',
        'age': 24,
        'visits': 758,
        'progress': 96,
        'createdAt': new Date('2012-11-16T04:55:01.264Z'),
        'status': 'single',
        'company': 'Hoffmann-Schenk',
        'wealth': {
          'currency': 'USD',
          'amount': -80791,
        },
        'subRows': [
          {
            'firstName': 'Erika',
            'lastName': 'Hirt',
            'age': 39,
            'visits': 942,
            'progress': 50,
            'createdAt': new Date('1997-03-17T03:27:30.589Z'),
            'status': 'relationship',
            'company': 'Mettler-Wirth',
            'wealth': {
              'currency': 'CHF',
              'amount': -6606,
            },
          },
          {
            'firstName': 'Mario',
            'lastName': 'Geiger',
            'age': 8,
            'visits': 735,
            'progress': 64,
            'createdAt': new Date('2000-04-29T06:39:38.497Z'),
            'status': 'relationship',
            'company': 'Ferrari Inc.',
            'wealth': {
              'currency': 'CHF',
              'amount': 973835,
            },
          },
        ],
      },
      {
        'firstName': 'Astrid',
        'lastName': 'Bolliger',
        'age': 4,
        'visits': 250,
        'progress': 9,
        'createdAt': new Date('1995-04-09T16:26:31.947Z'),
        'status': 'single',
        'company': 'Rey AG',
        'wealth': {
          'currency': 'USD',
          'amount': 327783,
        },
        'subRows': [
          {
            'firstName': 'Margrit',
            'lastName': 'Ackermann',
            'age': 7,
            'visits': 829,
            'progress': 95,
            'createdAt': new Date('2017-02-08T01:11:48.643Z'),
            'status': 'relationship',
            'company': 'Sutter, Hofmann und Hotz',
            'wealth': {
              'currency': 'USD',
              'amount': 787878,
            },
          },
          {
            'firstName': 'Luigi',
            'lastName': 'Wolf',
            'age': 39,
            'visits': 527,
            'progress': 37,
            'createdAt': new Date('2021-05-09T23:52:52.133Z'),
            'status': 'relationship',
            'company': 'Wirz, Mettler und Roos',
            'wealth': {
              'currency': 'USD',
              'amount': -121858,
            },
          },
        ],
      },
      {
        'firstName': 'Antonio',
        'lastName': 'Arnold',
        'age': 36,
        'visits': 251,
        'progress': 98,
        'createdAt': new Date('1998-10-27T04:18:03.731Z'),
        'status': 'single',
        'company': 'Seiler-Thommen',
        'wealth': {
          'currency': 'USD',
          'amount': 829633,
        },
        'subRows': [
          {
            'firstName': 'Joseph',
            'lastName': 'Siegrist',
            'age': 25,
            'visits': 955,
            'progress': 82,
            'createdAt': new Date('1995-01-31T13:33:53.756Z'),
            'status': 'single',
            'company': 'Furrer GmbH',
            'wealth': {
              'currency': 'CHF',
              'amount': 68249,
            },
          },
          {
            'firstName': 'Samuel',
            'lastName': 'Schoch',
            'age': 6,
            'visits': 305,
            'progress': 9,
            'createdAt': new Date('1997-02-21T09:06:49.301Z'),
            'status': 'complicated',
            'company': 'Brun-Favre',
            'wealth': {
              'currency': 'USD',
              'amount': 767358,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Ueli',
    'lastName': 'Albrecht',
    'age': 8,
    'visits': 994,
    'progress': 98,
    'createdAt': new Date('2022-03-18T00:05:49.652Z'),
    'status': 'single',
    'company': 'Müller, Wirz und Hunziker',
    'wealth': {
      'currency': 'EUR',
      'amount': 957348,
    },
    'subRows': [
      {
        'firstName': 'Yolande',
        'lastName': 'Näf',
        'age': 39,
        'visits': 110,
        'progress': 15,
        'createdAt': new Date('2000-02-18T23:45:38.577Z'),
        'status': 'single',
        'company': 'Schweizer-Brun',
        'wealth': {
          'currency': 'EUR',
          'amount': -159513,
        },
        'subRows': [
          {
            'firstName': 'Wolfgang',
            'lastName': 'Näf',
            'age': 7,
            'visits': 959,
            'progress': 80,
            'createdAt': new Date('1994-12-27T23:14:40.332Z'),
            'status': 'single',
            'company': 'Zürcher-Gloor',
            'wealth': {
              'currency': 'USD',
              'amount': 712835,
            },
          },
          {
            'firstName': 'Caroline',
            'lastName': 'Bättig',
            'age': 15,
            'visits': 171,
            'progress': 32,
            'createdAt': new Date('2004-04-10T10:51:13.054Z'),
            'status': 'single',
            'company': 'Steinmann, Isler und Ernst',
            'wealth': {
              'currency': 'EUR',
              'amount': 978986,
            },
          },
        ],
      },
      {
        'firstName': 'Sabine',
        'lastName': 'Winkler',
        'age': 17,
        'visits': 621,
        'progress': 28,
        'createdAt': new Date('1999-07-18T11:00:58.214Z'),
        'status': 'relationship',
        'company': 'Blum-Widmer',
        'wealth': {
          'currency': 'USD',
          'amount': 486176,
        },
        'subRows': [
          {
            'firstName': 'Vreni',
            'lastName': 'Schumacher',
            'age': 3,
            'visits': 391,
            'progress': 63,
            'createdAt': new Date('2015-06-16T00:18:44.964Z'),
            'status': 'single',
            'company': 'Scheidegger, Wirth und Kaufmann',
            'wealth': {
              'currency': 'CHF',
              'amount': 677041,
            },
          },
          {
            'firstName': 'Denis',
            'lastName': 'Senn',
            'age': 31,
            'visits': 91,
            'progress': 48,
            'createdAt': new Date('2008-06-07T14:43:08.331Z'),
            'status': 'complicated',
            'company': 'Steinmann, Egger und Hofmann',
            'wealth': {
              'currency': 'CHF',
              'amount': 862800,
            },
          },
        ],
      },
      {
        'firstName': 'Marianne',
        'lastName': 'Weibel',
        'age': 29,
        'visits': 699,
        'progress': 76,
        'createdAt': new Date('2009-03-30T18:51:47.039Z'),
        'status': 'relationship',
        'company': 'Favre-Scherrer',
        'wealth': {
          'currency': 'USD',
          'amount': 480513,
        },
        'subRows': [
          {
            'firstName': 'Gisela',
            'lastName': 'Hug',
            'age': 10,
            'visits': 156,
            'progress': 76,
            'createdAt': new Date('2015-04-14T11:52:34.776Z'),
            'status': 'single',
            'company': 'Widmer, Tobler und Schuler',
            'wealth': {
              'currency': 'CHF',
              'amount': 352652,
            },
          },
          {
            'firstName': 'Esther',
            'lastName': 'Zimmermann',
            'age': 27,
            'visits': 445,
            'progress': 54,
            'createdAt': new Date('1990-05-09T12:42:05.211Z'),
            'status': 'single',
            'company': 'Bosshard-Stadelmann',
            'wealth': {
              'currency': 'EUR',
              'amount': 820565,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Jacques',
    'lastName': 'Merz',
    'age': 6,
    'visits': 74,
    'progress': 63,
    'createdAt': new Date('1990-07-13T02:50:19.869Z'),
    'status': 'single',
    'company': 'Ferrari-Hofstetter',
    'wealth': {
      'currency': 'CHF',
      'amount': -43552,
    },
    'subRows': [
      {
        'firstName': 'Ursula',
        'lastName': 'Zimmermann',
        'age': 8,
        'visits': 590,
        'progress': 15,
        'createdAt': new Date('2010-06-12T20:10:43.768Z'),
        'status': 'single',
        'company': 'Jäggi, Käser und Wirz',
        'wealth': {
          'currency': 'EUR',
          'amount': 402508,
        },
        'subRows': [
          {
            'firstName': 'Regula',
            'lastName': 'Kaiser',
            'age': 5,
            'visits': 955,
            'progress': 51,
            'createdAt': new Date('1991-08-28T21:28:46.866Z'),
            'status': 'relationship',
            'company': 'Leunberger, Pfister und Berger',
            'wealth': {
              'currency': 'EUR',
              'amount': 908011,
            },
          },
          {
            'firstName': 'Helene',
            'lastName': 'Ackermann',
            'age': 11,
            'visits': 333,
            'progress': 51,
            'createdAt': new Date('2009-10-27T02:33:59.209Z'),
            'status': 'single',
            'company': 'Schneider, Bauer und Lehmann',
            'wealth': {
              'currency': 'EUR',
              'amount': 43875,
            },
          },
        ],
      },
      {
        'firstName': 'Vreni',
        'lastName': 'Lustenberger',
        'age': 11,
        'visits': 939,
        'progress': 26,
        'createdAt': new Date('1995-11-26T19:49:25.875Z'),
        'status': 'relationship',
        'company': 'Ritter-Kessler',
        'wealth': {
          'currency': 'EUR',
          'amount': 415594,
        },
        'subRows': [
          {
            'firstName': 'Gérard',
            'lastName': 'Probst',
            'age': 33,
            'visits': 672,
            'progress': 83,
            'createdAt': new Date('1995-05-19T23:38:31.626Z'),
            'status': 'complicated',
            'company': 'Portmann-Müller',
            'wealth': {
              'currency': 'USD',
              'amount': 472100,
            },
          },
          {
            'firstName': 'Markus',
            'lastName': 'Winkler',
            'age': 1,
            'visits': 941,
            'progress': 77,
            'createdAt': new Date('2008-10-23T19:49:23.000Z'),
            'status': 'single',
            'company': 'Hofmann-Geiger',
            'wealth': {
              'currency': 'USD',
              'amount': -31223,
            },
          },
        ],
      },
      {
        'firstName': 'Nelly',
        'lastName': 'Schnyder',
        'age': 6,
        'visits': 54,
        'progress': 10,
        'createdAt': new Date('2002-06-05T01:03:30.565Z'),
        'status': 'single',
        'company': 'Ammann, Hofer und Albrecht',
        'wealth': {
          'currency': 'CHF',
          'amount': 693719,
        },
        'subRows': [
          {
            'firstName': 'Heinrich',
            'lastName': 'Kunz',
            'age': 10,
            'visits': 397,
            'progress': 93,
            'createdAt': new Date('1998-12-14T20:00:38.441Z'),
            'status': 'complicated',
            'company': 'Hoffmann und Partner',
            'wealth': {
              'currency': 'USD',
              'amount': 66582,
            },
          },
          {
            'firstName': 'Max',
            'lastName': 'Knecht',
            'age': 30,
            'visits': 134,
            'progress': 13,
            'createdAt': new Date('2021-04-12T03:07:32.978Z'),
            'status': 'single',
            'company': 'Zürcher-Koch',
            'wealth': {
              'currency': 'CHF',
              'amount': -197791,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Jean-Marie',
    'lastName': 'Wyss',
    'age': 10,
    'visits': 618,
    'progress': 73,
    'createdAt': new Date('1999-05-22T03:10:26.284Z'),
    'status': 'complicated',
    'company': 'Gut Inc.',
    'wealth': {
      'currency': 'USD',
      'amount': -109040,
    },
    'subRows': [
      {
        'firstName': 'Markus',
        'lastName': 'Stettler',
        'age': 24,
        'visits': 590,
        'progress': 62,
        'createdAt': new Date('1999-01-24T06:43:42.998Z'),
        'status': 'single',
        'company': 'Schoch-Bolliger',
        'wealth': {
          'currency': 'EUR',
          'amount': 492578,
        },
        'subRows': [
          {
            'firstName': 'Gérald',
            'lastName': 'Maurer',
            'age': 3,
            'visits': 272,
            'progress': 20,
            'createdAt': new Date('1994-05-07T19:44:41.250Z'),
            'status': 'relationship',
            'company': 'Kunz, Gfeller und Wirth',
            'wealth': {
              'currency': 'CHF',
              'amount': 966712,
            },
          },
          {
            'firstName': 'Dominique',
            'lastName': 'Stettler',
            'age': 31,
            'visits': 59,
            'progress': 32,
            'createdAt': new Date('2016-03-25T21:52:07.885Z'),
            'status': 'relationship',
            'company': 'Peter LLC',
            'wealth': {
              'currency': 'EUR',
              'amount': 699588,
            },
          },
        ],
      },
      {
        'firstName': 'Emil',
        'lastName': 'Albrecht',
        'age': 11,
        'visits': 261,
        'progress': 48,
        'createdAt': new Date('2020-08-23T16:39:27.837Z'),
        'status': 'complicated',
        'company': 'Pfister, Schneider und Meister',
        'wealth': {
          'currency': 'EUR',
          'amount': 351713,
        },
        'subRows': [
          {
            'firstName': 'Chantal',
            'lastName': 'Gross',
            'age': 29,
            'visits': 57,
            'progress': 4,
            'createdAt': new Date('2020-09-08T17:46:39.555Z'),
            'status': 'complicated',
            'company': 'Eugster-Stöckli',
            'wealth': {
              'currency': 'EUR',
              'amount': 204285,
            },
          },
          {
            'firstName': 'Jürg',
            'lastName': 'Lehmann',
            'age': 13,
            'visits': 206,
            'progress': 87,
            'createdAt': new Date('2018-11-14T02:02:59.493Z'),
            'status': 'complicated',
            'company': 'Martin AG',
            'wealth': {
              'currency': 'USD',
              'amount': 752724,
            },
          },
        ],
      },
      {
        'firstName': 'Antoine',
        'lastName': 'Schaller',
        'age': 3,
        'visits': 856,
        'progress': 18,
        'createdAt': new Date('1992-01-29T19:36:38.373Z'),
        'status': 'single',
        'company': 'Knecht & Co.',
        'wealth': {
          'currency': 'EUR',
          'amount': 9318,
        },
        'subRows': [
          {
            'firstName': 'Yolande',
            'lastName': 'Beck',
            'age': 38,
            'visits': 8,
            'progress': 99,
            'createdAt': new Date('1995-09-11T04:23:26.461Z'),
            'status': 'relationship',
            'company': 'Gerber, Frei und Kuhn',
            'wealth': {
              'currency': 'EUR',
              'amount': 388073,
            },
          },
          {
            'firstName': 'Thomas',
            'lastName': 'Hürlimann',
            'age': 5,
            'visits': 27,
            'progress': 25,
            'createdAt': new Date('1996-09-27T15:56:33.896Z'),
            'status': 'relationship',
            'company': 'Lanz Gruppe',
            'wealth': {
              'currency': 'CHF',
              'amount': 439468,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Johann',
    'lastName': 'Schuler',
    'age': 1,
    'visits': 609,
    'progress': 60,
    'createdAt': new Date('2011-09-19T12:16:53.479Z'),
    'status': 'relationship',
    'company': 'Furrer-Gerber',
    'wealth': {
      'currency': 'CHF',
      'amount': 267794,
    },
    'subRows': [
      {
        'firstName': 'Helene',
        'lastName': 'Stauffer',
        'age': 0,
        'visits': 345,
        'progress': 20,
        'createdAt': new Date('2003-07-05T10:28:22.609Z'),
        'status': 'single',
        'company': 'Bühler-Benz',
        'wealth': {
          'currency': 'CHF',
          'amount': 524960,
        },
        'subRows': [
          {
            'firstName': 'Janine',
            'lastName': 'Lüthi',
            'age': 35,
            'visits': 278,
            'progress': 89,
            'createdAt': new Date('1998-11-10T20:36:08.490Z'),
            'status': 'relationship',
            'company': 'Felder und Söhne',
            'wealth': {
              'currency': 'USD',
              'amount': 500285,
            },
          },
          {
            'firstName': 'Antoine',
            'lastName': 'Staub',
            'age': 26,
            'visits': 891,
            'progress': 56,
            'createdAt': new Date('2021-11-19T15:46:06.045Z'),
            'status': 'complicated',
            'company': 'Isler-Maier',
            'wealth': {
              'currency': 'USD',
              'amount': 822783,
            },
          },
        ],
      },
      {
        'firstName': 'Alois',
        'lastName': 'Ferrari',
        'age': 0,
        'visits': 176,
        'progress': 77,
        'createdAt': new Date('2002-05-05T05:18:08.725Z'),
        'status': 'single',
        'company': 'Ackermann Inc.',
        'wealth': {
          'currency': 'EUR',
          'amount': 653588,
        },
        'subRows': [
          {
            'firstName': 'Stephan',
            'lastName': 'Hermann',
            'age': 3,
            'visits': 44,
            'progress': 79,
            'createdAt': new Date('2008-02-15T21:09:15.996Z'),
            'status': 'relationship',
            'company': 'Hodel-Roos',
            'wealth': {
              'currency': 'USD',
              'amount': 313214,
            },
          },
          {
            'firstName': 'Astrid',
            'lastName': 'Jost',
            'age': 12,
            'visits': 721,
            'progress': 59,
            'createdAt': new Date('2023-08-14T04:17:00.145Z'),
            'status': 'relationship',
            'company': 'Schwab-Fischer',
            'wealth': {
              'currency': 'EUR',
              'amount': 927502,
            },
          },
        ],
      },
      {
        'firstName': 'Corinne',
        'lastName': 'Tanner',
        'age': 11,
        'visits': 813,
        'progress': 21,
        'createdAt': new Date('1990-06-05T18:30:14.584Z'),
        'status': 'complicated',
        'company': 'Blum GmbH',
        'wealth': {
          'currency': 'USD',
          'amount': 625750,
        },
        'subRows': [
          {
            'firstName': 'Lydia',
            'lastName': 'Berger',
            'age': 0,
            'visits': 499,
            'progress': 0,
            'createdAt': new Date('2011-06-28T17:13:01.304Z'),
            'status': 'relationship',
            'company': 'Probst, Bianchi und Scherrer',
            'wealth': {
              'currency': 'CHF',
              'amount': 515639,
            },
          },
          {
            'firstName': 'Florence',
            'lastName': 'Felber',
            'age': 37,
            'visits': 110,
            'progress': 62,
            'createdAt': new Date('2016-07-31T12:47:34.780Z'),
            'status': 'single',
            'company': 'Favre, Kern und Hirt',
            'wealth': {
              'currency': 'EUR',
              'amount': 67988,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Ferdinand',
    'lastName': 'Baumgartner',
    'age': 1,
    'visits': 319,
    'progress': 12,
    'createdAt': new Date('2017-11-02T09:48:14.192Z'),
    'status': 'single',
    'company': 'Stalder GmbH',
    'wealth': {
      'currency': 'EUR',
      'amount': 423505,
    },
    'subRows': [
      {
        'firstName': 'Jean-Marc',
        'lastName': 'Peter',
        'age': 19,
        'visits': 87,
        'progress': 10,
        'createdAt': new Date('2017-01-11T04:26:41.954Z'),
        'status': 'relationship',
        'company': 'Lustenberger, Maier und Schär',
        'wealth': {
          'currency': 'CHF',
          'amount': 409772,
        },
        'subRows': [
          {
            'firstName': 'Helmut',
            'lastName': 'Gross',
            'age': 18,
            'visits': 986,
            'progress': 6,
            'createdAt': new Date('2014-05-01T19:09:58.596Z'),
            'status': 'relationship',
            'company': 'Pfister LLC',
            'wealth': {
              'currency': 'CHF',
              'amount': 627056,
            },
          },
          {
            'firstName': 'Sylvie',
            'lastName': 'Müller',
            'age': 27,
            'visits': 673,
            'progress': 96,
            'createdAt': new Date('2023-11-03T11:36:10.365Z'),
            'status': 'complicated',
            'company': 'Rüegg-Schäfer',
            'wealth': {
              'currency': 'USD',
              'amount': 384688,
            },
          },
        ],
      },
      {
        'firstName': 'Antonio',
        'lastName': 'Wüthrich',
        'age': 21,
        'visits': 976,
        'progress': 68,
        'createdAt': new Date('1991-08-14T00:51:17.899Z'),
        'status': 'relationship',
        'company': 'Zollinger-Müller',
        'wealth': {
          'currency': 'CHF',
          'amount': 340900,
        },
        'subRows': [
          {
            'firstName': 'Claire',
            'lastName': 'Bühler',
            'age': 15,
            'visits': 727,
            'progress': 38,
            'createdAt': new Date('2004-11-06T19:28:51.032Z'),
            'status': 'single',
            'company': 'Moser LLC',
            'wealth': {
              'currency': 'EUR',
              'amount': 594425,
            },
          },
          {
            'firstName': 'Ruth',
            'lastName': 'Rey',
            'age': 21,
            'visits': 340,
            'progress': 54,
            'createdAt': new Date('2006-06-23T23:18:24.269Z'),
            'status': 'single',
            'company': 'Schürch-Schürch',
            'wealth': {
              'currency': 'EUR',
              'amount': -12568,
            },
          },
        ],
      },
      {
        'firstName': 'Corinne',
        'lastName': 'Marti',
        'age': 34,
        'visits': 86,
        'progress': 31,
        'createdAt': new Date('2005-12-19T07:40:27.117Z'),
        'status': 'complicated',
        'company': 'Christen-Schlatter',
        'wealth': {
          'currency': 'EUR',
          'amount': 926328,
        },
        'subRows': [
          {
            'firstName': 'Ernst',
            'lastName': 'Bühler',
            'age': 40,
            'visits': 519,
            'progress': 18,
            'createdAt': new Date('2001-02-28T10:19:06.224Z'),
            'status': 'complicated',
            'company': 'Berger-Amrein',
            'wealth': {
              'currency': 'EUR',
              'amount': 12499,
            },
          },
          {
            'firstName': 'Françoise',
            'lastName': 'Stöckli',
            'age': 23,
            'visits': 330,
            'progress': 93,
            'createdAt': new Date('1992-02-17T04:17:48.426Z'),
            'status': 'relationship',
            'company': 'Gloor-Stutz',
            'wealth': {
              'currency': 'CHF',
              'amount': 484533,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Georges',
    'lastName': 'Meier',
    'age': 3,
    'visits': 870,
    'progress': 23,
    'createdAt': new Date('2009-03-30T22:25:09.261Z'),
    'status': 'relationship',
    'company': 'Hürlimann-Merz',
    'wealth': {
      'currency': 'EUR',
      'amount': -27030,
    },
    'subRows': [
      {
        'firstName': 'Liliane',
        'lastName': 'Kunz',
        'age': 14,
        'visits': 820,
        'progress': 85,
        'createdAt': new Date('2005-07-06T20:57:44.728Z'),
        'status': 'relationship',
        'company': 'Kägi-Merz',
        'wealth': {
          'currency': 'EUR',
          'amount': 495333,
        },
        'subRows': [
          {
            'firstName': 'Hanna',
            'lastName': 'Lustenberger',
            'age': 2,
            'visits': 116,
            'progress': 1,
            'createdAt': new Date('2001-10-26T13:17:07.893Z'),
            'status': 'relationship',
            'company': 'Hermann AG',
            'wealth': {
              'currency': 'USD',
              'amount': 815882,
            },
          },
          {
            'firstName': 'Josette',
            'lastName': 'Walter',
            'age': 34,
            'visits': 599,
            'progress': 79,
            'createdAt': new Date('2009-12-05T21:33:30.808Z'),
            'status': 'single',
            'company': 'Thommen und Söhne',
            'wealth': {
              'currency': 'CHF',
              'amount': 517952,
            },
          },
        ],
      },
      {
        'firstName': 'Didier',
        'lastName': 'Rüegg',
        'age': 35,
        'visits': 891,
        'progress': 16,
        'createdAt': new Date('1990-09-03T13:36:13.627Z'),
        'status': 'single',
        'company': 'Bauer & Co.',
        'wealth': {
          'currency': 'USD',
          'amount': 341707,
        },
        'subRows': [
          {
            'firstName': 'Rudolf',
            'lastName': 'Schnyder',
            'age': 2,
            'visits': 101,
            'progress': 97,
            'createdAt': new Date('2008-04-06T13:12:54.463Z'),
            'status': 'single',
            'company': 'Gloor GmbH',
            'wealth': {
              'currency': 'CHF',
              'amount': 361275,
            },
          },
          {
            'firstName': 'Franco',
            'lastName': 'Ott',
            'age': 33,
            'visits': 921,
            'progress': 0,
            'createdAt': new Date('2007-08-31T02:56:32.423Z'),
            'status': 'complicated',
            'company': 'Roth LLC',
            'wealth': {
              'currency': 'EUR',
              'amount': 138252,
            },
          },
        ],
      },
      {
        'firstName': 'Arthur',
        'lastName': 'Portmann',
        'age': 8,
        'visits': 784,
        'progress': 53,
        'createdAt': new Date('2003-12-30T23:43:11.059Z'),
        'status': 'complicated',
        'company': 'Rüegg Inc.',
        'wealth': {
          'currency': 'EUR',
          'amount': 479627,
        },
        'subRows': [
          {
            'firstName': 'Helena',
            'lastName': 'Kaiser',
            'age': 28,
            'visits': 360,
            'progress': 4,
            'createdAt': new Date('1994-08-22T02:51:27.995Z'),
            'status': 'single',
            'company': 'Schweizer, Jenni und Giger',
            'wealth': {
              'currency': 'EUR',
              'amount': 65342,
            },
          },
          {
            'firstName': 'Caroline',
            'lastName': 'Stauffer',
            'age': 14,
            'visits': 841,
            'progress': 9,
            'createdAt': new Date('1993-06-03T19:16:50.708Z'),
            'status': 'complicated',
            'company': 'Lanz & Co.',
            'wealth': {
              'currency': 'EUR',
              'amount': 941445,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Marianne',
    'lastName': 'Eugster',
    'age': 0,
    'visits': 418,
    'progress': 45,
    'createdAt': new Date('1990-09-26T07:47:45.666Z'),
    'status': 'relationship',
    'company': 'Bernasconi Gruppe',
    'wealth': {
      'currency': 'USD',
      'amount': 15688,
    },
    'subRows': [
      {
        'firstName': 'Johannes',
        'lastName': 'Ammann',
        'age': 20,
        'visits': 740,
        'progress': 94,
        'createdAt': new Date('1991-01-10T19:43:41.532Z'),
        'status': 'relationship',
        'company': 'Blum & Co.',
        'wealth': {
          'currency': 'EUR',
          'amount': 269476,
        },
        'subRows': [
          {
            'firstName': 'Michel',
            'lastName': 'Michel',
            'age': 21,
            'visits': 444,
            'progress': 79,
            'createdAt': new Date('2021-04-29T22:31:04.897Z'),
            'status': 'complicated',
            'company': 'Thommen LLC',
            'wealth': {
              'currency': 'EUR',
              'amount': 277587,
            },
          },
          {
            'firstName': 'Iris',
            'lastName': 'Kaufmann',
            'age': 23,
            'visits': 563,
            'progress': 96,
            'createdAt': new Date('1996-06-05T22:03:39.084Z'),
            'status': 'complicated',
            'company': 'Sutter-Aebi',
            'wealth': {
              'currency': 'EUR',
              'amount': 231563,
            },
          },
        ],
      },
      {
        'firstName': 'Chantal',
        'lastName': 'Wegmann',
        'age': 32,
        'visits': 908,
        'progress': 92,
        'createdAt': new Date('1993-02-02T06:47:51.142Z'),
        'status': 'single',
        'company': 'Walter AG',
        'wealth': {
          'currency': 'CHF',
          'amount': -98493,
        },
        'subRows': [
          {
            'firstName': 'Louis',
            'lastName': 'Brunner',
            'age': 16,
            'visits': 884,
            'progress': 86,
            'createdAt': new Date('1999-02-25T13:57:14.268Z'),
            'status': 'complicated',
            'company': 'Steffen und Söhne',
            'wealth': {
              'currency': 'USD',
              'amount': 319412,
            },
          },
          {
            'firstName': 'Georges',
            'lastName': 'Brun',
            'age': 29,
            'visits': 313,
            'progress': 90,
            'createdAt': new Date('2017-04-06T15:45:32.494Z'),
            'status': 'complicated',
            'company': 'Rüegg Gruppe',
            'wealth': {
              'currency': 'USD',
              'amount': 514334,
            },
          },
        ],
      },
      {
        'firstName': 'Marlies',
        'lastName': 'Schäfer',
        'age': 17,
        'visits': 267,
        'progress': 28,
        'createdAt': new Date('2014-05-29T00:07:37.285Z'),
        'status': 'relationship',
        'company': 'Rey-Erni',
        'wealth': {
          'currency': 'CHF',
          'amount': -72456,
        },
        'subRows': [
          {
            'firstName': 'Jean-François',
            'lastName': 'Friedli',
            'age': 18,
            'visits': 326,
            'progress': 31,
            'createdAt': new Date('2003-12-31T23:03:45.775Z'),
            'status': 'single',
            'company': 'Knecht & Co.',
            'wealth': {
              'currency': 'EUR',
              'amount': 760221,
            },
          },
          {
            'firstName': 'Walter',
            'lastName': 'Ammann',
            'age': 26,
            'visits': 586,
            'progress': 81,
            'createdAt': new Date('1990-01-26T03:13:11.890Z'),
            'status': 'relationship',
            'company': 'Baur & Co.',
            'wealth': {
              'currency': 'EUR',
              'amount': -11033,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Josef',
    'lastName': 'Imhof',
    'age': 15,
    'visits': 793,
    'progress': 24,
    'createdAt': new Date('1999-08-30T20:33:12.411Z'),
    'status': 'single',
    'company': 'Forster, Geiger und Zürcher',
    'wealth': {
      'currency': 'USD',
      'amount': 437295,
    },
    'subRows': [
      {
        'firstName': 'Carmen',
        'lastName': 'Steiner',
        'age': 24,
        'visits': 250,
        'progress': 47,
        'createdAt': new Date('2018-02-27T15:43:13.387Z'),
        'status': 'complicated',
        'company': 'Eichenberger-Stettler',
        'wealth': {
          'currency': 'USD',
          'amount': 304261,
        },
        'subRows': [
          {
            'firstName': 'Jürg',
            'lastName': 'Gut',
            'age': 14,
            'visits': 208,
            'progress': 55,
            'createdAt': new Date('2018-08-05T15:51:26.261Z'),
            'status': 'relationship',
            'company': 'Bolliger Gruppe',
            'wealth': {
              'currency': 'EUR',
              'amount': 523501,
            },
          },
          {
            'firstName': 'Pascal',
            'lastName': 'Scheidegger',
            'age': 3,
            'visits': 90,
            'progress': 26,
            'createdAt': new Date('2011-04-17T02:01:40.335Z'),
            'status': 'relationship',
            'company': 'Koch, Spörri und Huber',
            'wealth': {
              'currency': 'EUR',
              'amount': 838234,
            },
          },
        ],
      },
      {
        'firstName': 'Eveline',
        'lastName': 'Kälin',
        'age': 30,
        'visits': 708,
        'progress': 81,
        'createdAt': new Date('2011-05-08T18:02:59.594Z'),
        'status': 'complicated',
        'company': 'Wettstein-Bucher',
        'wealth': {
          'currency': 'EUR',
          'amount': 840777,
        },
        'subRows': [
          {
            'firstName': 'Frank',
            'lastName': 'Hess',
            'age': 7,
            'visits': 3,
            'progress': 39,
            'createdAt': new Date('1998-05-16T17:20:50.234Z'),
            'status': 'single',
            'company': 'Eugster, Kessler und Meier',
            'wealth': {
              'currency': 'USD',
              'amount': 800417,
            },
          },
          {
            'firstName': 'Christian',
            'lastName': 'Vogt',
            'age': 14,
            'visits': 826,
            'progress': 64,
            'createdAt': new Date('2018-03-30T17:14:28.919Z'),
            'status': 'single',
            'company': 'Beck Gruppe',
            'wealth': {
              'currency': 'EUR',
              'amount': 555609,
            },
          },
        ],
      },
      {
        'firstName': 'Monique',
        'lastName': 'Kaiser',
        'age': 2,
        'visits': 70,
        'progress': 31,
        'createdAt': new Date('2023-04-22T00:49:02.762Z'),
        'status': 'complicated',
        'company': 'Marti-Keller',
        'wealth': {
          'currency': 'CHF',
          'amount': 437305,
        },
        'subRows': [
          {
            'firstName': 'Nathalie',
            'lastName': 'Kuhn',
            'age': 23,
            'visits': 800,
            'progress': 31,
            'createdAt': new Date('2004-03-29T17:52:06.092Z'),
            'status': 'relationship',
            'company': 'Hoffmann-Wirth',
            'wealth': {
              'currency': 'EUR',
              'amount': 332768,
            },
          },
          {
            'firstName': 'Gabrielle',
            'lastName': 'Hess',
            'age': 28,
            'visits': 224,
            'progress': 28,
            'createdAt': new Date('2006-01-12T16:09:01.535Z'),
            'status': 'complicated',
            'company': 'Stutz-Amrein',
            'wealth': {
              'currency': 'EUR',
              'amount': 433473,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Vreni',
    'lastName': 'Jenni',
    'age': 20,
    'visits': 440,
    'progress': 2,
    'createdAt': new Date('2006-01-09T15:33:01.069Z'),
    'status': 'complicated',
    'company': 'Martin, Näf und Schuler',
    'wealth': {
      'currency': 'EUR',
      'amount': -85632,
    },
    'subRows': [
      {
        'firstName': 'Antonio',
        'lastName': 'Käser',
        'age': 3,
        'visits': 33,
        'progress': 48,
        'createdAt': new Date('2010-12-13T23:06:29.143Z'),
        'status': 'single',
        'company': 'Fehr-Brunner',
        'wealth': {
          'currency': 'EUR',
          'amount': 241957,
        },
        'subRows': [
          {
            'firstName': 'Therese',
            'lastName': 'Eugster',
            'age': 6,
            'visits': 26,
            'progress': 82,
            'createdAt': new Date('2020-02-19T09:41:31.147Z'),
            'status': 'relationship',
            'company': 'Hodel-Jäggi',
            'wealth': {
              'currency': 'EUR',
              'amount': 899587,
            },
          },
          {
            'firstName': 'Willy',
            'lastName': 'Schaub',
            'age': 31,
            'visits': 148,
            'progress': 18,
            'createdAt': new Date('2011-07-05T10:28:18.416Z'),
            'status': 'relationship',
            'company': 'Martin-Hofstetter',
            'wealth': {
              'currency': 'USD',
              'amount': 82966,
            },
          },
        ],
      },
      {
        'firstName': 'Hans-Ulrich',
        'lastName': 'Leunberger',
        'age': 7,
        'visits': 364,
        'progress': 45,
        'createdAt': new Date('2008-02-24T00:19:58.423Z'),
        'status': 'single',
        'company': 'Winkler, Gloor und Hotz',
        'wealth': {
          'currency': 'CHF',
          'amount': 577094,
        },
        'subRows': [
          {
            'firstName': 'Christina',
            'lastName': 'Frei',
            'age': 35,
            'visits': 508,
            'progress': 20,
            'createdAt': new Date('1999-06-13T13:00:52.232Z'),
            'status': 'single',
            'company': 'Frey, Felber und Maier',
            'wealth': {
              'currency': 'CHF',
              'amount': 179849,
            },
          },
          {
            'firstName': 'Madeleine',
            'lastName': 'Hafner',
            'age': 25,
            'visits': 70,
            'progress': 45,
            'createdAt': new Date('2019-05-15T08:36:58.184Z'),
            'status': 'relationship',
            'company': 'Betschart LLC',
            'wealth': {
              'currency': 'CHF',
              'amount': 969297,
            },
          },
        ],
      },
      {
        'firstName': 'Nelly',
        'lastName': 'Ott',
        'age': 12,
        'visits': 344,
        'progress': 67,
        'createdAt': new Date('2010-06-10T00:03:58.751Z'),
        'status': 'single',
        'company': 'Lehmann-Schürch',
        'wealth': {
          'currency': 'CHF',
          'amount': -75938,
        },
        'subRows': [
          {
            'firstName': 'Anne-Marie',
            'lastName': 'Hauser',
            'age': 6,
            'visits': 387,
            'progress': 17,
            'createdAt': new Date('1997-10-18T23:59:36.842Z'),
            'status': 'relationship',
            'company': 'Favre, Flückiger und Jost',
            'wealth': {
              'currency': 'EUR',
              'amount': 76701,
            },
          },
          {
            'firstName': 'Louis',
            'lastName': 'Koch',
            'age': 16,
            'visits': 153,
            'progress': 37,
            'createdAt': new Date('2014-08-06T00:40:59.358Z'),
            'status': 'single',
            'company': 'Jäggi, Schnyder und Hürlimann',
            'wealth': {
              'currency': 'USD',
              'amount': 448003,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Michael',
    'lastName': 'Baur',
    'age': 33,
    'visits': 710,
    'progress': 27,
    'createdAt': new Date('2005-11-08T06:49:51.621Z'),
    'status': 'relationship',
    'company': 'Huber AG',
    'wealth': {
      'currency': 'EUR',
      'amount': -128148,
    },
    'subRows': [
      {
        'firstName': 'Angela',
        'lastName': 'Häfliger',
        'age': 8,
        'visits': 436,
        'progress': 20,
        'createdAt': new Date('2020-08-09T07:56:31.681Z'),
        'status': 'relationship',
        'company': 'Zehnder, Häfliger und Scherrer',
        'wealth': {
          'currency': 'CHF',
          'amount': -30140,
        },
        'subRows': [
          {
            'firstName': 'Didier',
            'lastName': 'Baumgartner',
            'age': 21,
            'visits': 741,
            'progress': 97,
            'createdAt': new Date('1991-11-19T02:06:27.950Z'),
            'status': 'single',
            'company': 'Schmid AG',
            'wealth': {
              'currency': 'USD',
              'amount': 569547,
            },
          },
          {
            'firstName': 'Friedrich',
            'lastName': 'Blaser',
            'age': 20,
            'visits': 546,
            'progress': 10,
            'createdAt': new Date('2018-02-18T20:11:10.785Z'),
            'status': 'single',
            'company': 'Fehr, Roth und Jenni',
            'wealth': {
              'currency': 'USD',
              'amount': 743017,
            },
          },
        ],
      },
      {
        'firstName': 'Gertrud',
        'lastName': 'Schwab',
        'age': 21,
        'visits': 638,
        'progress': 65,
        'createdAt': new Date('2008-12-04T01:01:32.081Z'),
        'status': 'single',
        'company': 'Bader-Blaser',
        'wealth': {
          'currency': 'CHF',
          'amount': 10337,
        },
        'subRows': [
          {
            'firstName': 'David',
            'lastName': 'Marti',
            'age': 4,
            'visits': 284,
            'progress': 84,
            'createdAt': new Date('2012-07-29T11:40:53.823Z'),
            'status': 'relationship',
            'company': 'Eichenberger Gruppe',
            'wealth': {
              'currency': 'CHF',
              'amount': 915137,
            },
          },
          {
            'firstName': 'Elsa',
            'lastName': 'Koch',
            'age': 19,
            'visits': 163,
            'progress': 26,
            'createdAt': new Date('2013-07-21T11:44:32.907Z'),
            'status': 'single',
            'company': 'Hoffmann, Bachmann und Schoch',
            'wealth': {
              'currency': 'EUR',
              'amount': 199751,
            },
          },
        ],
      },
      {
        'firstName': 'Vreni',
        'lastName': 'Thommen',
        'age': 4,
        'visits': 770,
        'progress': 26,
        'createdAt': new Date('2010-10-22T21:46:30.604Z'),
        'status': 'complicated',
        'company': 'Berger, Knecht und Näf',
        'wealth': {
          'currency': 'USD',
          'amount': 752916,
        },
        'subRows': [
          {
            'firstName': 'Claire',
            'lastName': 'Leunberger',
            'age': 16,
            'visits': 97,
            'progress': 56,
            'createdAt': new Date('1994-09-10T06:10:47.684Z'),
            'status': 'relationship',
            'company': 'Probst, Baumann und Merz',
            'wealth': {
              'currency': 'CHF',
              'amount': 538259,
            },
          },
          {
            'firstName': 'Cornelia',
            'lastName': 'Wagner',
            'age': 34,
            'visits': 942,
            'progress': 60,
            'createdAt': new Date('2014-02-13T06:44:20.688Z'),
            'status': 'single',
            'company': 'Stocker-Bernasconi',
            'wealth': {
              'currency': 'EUR',
              'amount': 558114,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Eugen',
    'lastName': 'Hasler',
    'age': 4,
    'visits': 955,
    'progress': 42,
    'createdAt': new Date('2014-01-18T14:24:01.021Z'),
    'status': 'complicated',
    'company': 'Leu & Co.',
    'wealth': {
      'currency': 'CHF',
      'amount': 940543,
    },
    'subRows': [
      {
        'firstName': 'Nadia',
        'lastName': 'Kohler',
        'age': 14,
        'visits': 478,
        'progress': 17,
        'createdAt': new Date('2013-08-02T18:02:41.518Z'),
        'status': 'complicated',
        'company': 'Leunberger AG',
        'wealth': {
          'currency': 'EUR',
          'amount': 271121,
        },
        'subRows': [
          {
            'firstName': 'Gabriel',
            'lastName': 'Ackermann',
            'age': 27,
            'visits': 182,
            'progress': 33,
            'createdAt': new Date('2022-03-04T11:05:30.074Z'),
            'status': 'relationship',
            'company': 'Bianchi und Partner',
            'wealth': {
              'currency': 'CHF',
              'amount': 172029,
            },
          },
          {
            'firstName': 'Beat',
            'lastName': 'Tanner',
            'age': 27,
            'visits': 192,
            'progress': 98,
            'createdAt': new Date('2007-07-25T18:45:44.119Z'),
            'status': 'single',
            'company': 'Senn AG',
            'wealth': {
              'currency': 'EUR',
              'amount': 230314,
            },
          },
        ],
      },
      {
        'firstName': 'Laura',
        'lastName': 'Baumgartner',
        'age': 39,
        'visits': 870,
        'progress': 17,
        'createdAt': new Date('2014-08-12T15:20:13.964Z'),
        'status': 'single',
        'company': 'Iten Inc.',
        'wealth': {
          'currency': 'EUR',
          'amount': 155112,
        },
        'subRows': [
          {
            'firstName': 'Beatrix',
            'lastName': 'Thommen',
            'age': 14,
            'visits': 867,
            'progress': 24,
            'createdAt': new Date('2022-04-12T18:50:12.991Z'),
            'status': 'complicated',
            'company': 'Leu & Co.',
            'wealth': {
              'currency': 'CHF',
              'amount': 73200,
            },
          },
          {
            'firstName': 'Margrit',
            'lastName': 'Steiner',
            'age': 33,
            'visits': 24,
            'progress': 59,
            'createdAt': new Date('2005-06-03T23:07:09.823Z'),
            'status': 'single',
            'company': 'Haas-Ziegler',
            'wealth': {
              'currency': 'USD',
              'amount': 861687,
            },
          },
        ],
      },
      {
        'firstName': 'Véronique',
        'lastName': 'Gut',
        'age': 18,
        'visits': 613,
        'progress': 9,
        'createdAt': new Date('2022-06-20T01:24:16.207Z'),
        'status': 'single',
        'company': 'Benz und Söhne',
        'wealth': {
          'currency': 'CHF',
          'amount': 621620,
        },
        'subRows': [
          {
            'firstName': 'Renata',
            'lastName': 'Wettstein',
            'age': 15,
            'visits': 107,
            'progress': 53,
            'createdAt': new Date('1991-06-19T17:14:06.859Z'),
            'status': 'relationship',
            'company': 'Winkler-Benz',
            'wealth': {
              'currency': 'USD',
              'amount': -7238,
            },
          },
          {
            'firstName': 'Laurence',
            'lastName': 'Kessler',
            'age': 23,
            'visits': 619,
            'progress': 100,
            'createdAt': new Date('2009-07-16T21:17:22.030Z'),
            'status': 'single',
            'company': 'Graf-Maier',
            'wealth': {
              'currency': 'EUR',
              'amount': 738684,
            },
          },
        ],
      },
    ],
  },
  {
    'firstName': 'Joseph',
    'lastName': 'Frei',
    'age': 33,
    'visits': 467,
    'progress': 4,
    'createdAt': new Date('2012-01-11T21:20:06.231Z'),
    'status': 'single',
    'company': 'Schmidt AG',
    'wealth': {
      'currency': 'CHF',
      'amount': 428093,
    },
    'subRows': [
      {
        'firstName': 'Renato',
        'lastName': 'Flückiger',
        'age': 31,
        'visits': 864,
        'progress': 9,
        'createdAt': new Date('2005-09-16T04:33:58.221Z'),
        'status': 'relationship',
        'company': 'Baur & Co.',
        'wealth': {
          'currency': 'EUR',
          'amount': 963691,
        },
        'subRows': [
          {
            'firstName': 'Julia',
            'lastName': 'Fuchs',
            'age': 38,
            'visits': 578,
            'progress': 20,
            'createdAt': new Date('2019-11-04T14:54:34.777Z'),
            'status': 'relationship',
            'company': 'Ott-Peter',
            'wealth': {
              'currency': 'CHF',
              'amount': -80544,
            },
          },
          {
            'firstName': 'Philipp',
            'lastName': 'Fehr',
            'age': 37,
            'visits': 926,
            'progress': 26,
            'createdAt': new Date('2020-09-20T16:06:13.794Z'),
            'status': 'single',
            'company': 'Scheidegger LLC',
            'wealth': {
              'currency': 'USD',
              'amount': 691119,
            },
          },
        ],
      },
      {
        'firstName': 'Esther',
        'lastName': 'Zürcher',
        'age': 7,
        'visits': 812,
        'progress': 6,
        'createdAt': new Date('2005-06-03T00:59:26.288Z'),
        'status': 'complicated',
        'company': 'Käser GmbH',
        'wealth': {
          'currency': 'EUR',
          'amount': -149826,
        },
        'subRows': [
          {
            'firstName': 'Michele',
            'lastName': 'Bernasconi',
            'age': 10,
            'visits': 647,
            'progress': 70,
            'createdAt': new Date('2023-03-30T23:21:41.315Z'),
            'status': 'single',
            'company': 'Wehrli, Wyss und Zollinger',
            'wealth': {
              'currency': 'USD',
              'amount': 503959,
            },
          },
          {
            'firstName': 'Niklaus',
            'lastName': 'Stutz',
            'age': 30,
            'visits': 415,
            'progress': 3,
            'createdAt': new Date('2014-12-07T02:23:52.883Z'),
            'status': 'relationship',
            'company': 'Hofstetter-Wyss',
            'wealth': {
              'currency': 'EUR',
              'amount': 995797,
            },
          },
        ],
      },
      {
        'firstName': 'Henri',
        'lastName': 'Zingg',
        'age': 29,
        'visits': 79,
        'progress': 92,
        'createdAt': new Date('1992-11-08T10:35:15.619Z'),
        'status': 'relationship',
        'company': 'Tobler und Partner',
        'wealth': {
          'currency': 'CHF',
          'amount': -8491,
        },
        'subRows': [
          {
            'firstName': 'Brigitte',
            'lastName': 'Roos',
            'age': 17,
            'visits': 76,
            'progress': 38,
            'createdAt': new Date('2016-12-14T22:23:47.241Z'),
            'status': 'complicated',
            'company': 'Ackermann-Knecht',
            'wealth': {
              'currency': 'CHF',
              'amount': 953887,
            },
          },
          {
            'firstName': 'Erwin',
            'lastName': 'Ammann',
            'age': 21,
            'visits': 500,
            'progress': 95,
            'createdAt': new Date('2005-01-31T14:00:53.103Z'),
            'status': 'relationship',
            'company': 'Lüscher, Hauser und Suter',
            'wealth': {
              'currency': 'USD',
              'amount': 24002,
            },
          },
        ],
      },
    ],
  },
];
