import {Meta} from '@storybook/blocks';

<Meta title="Introduction" />

# SNF Design System

## Get Started

The SNF Design System is a reusable component library. All components are native web components, which allow to be used
in any framework.

## Specification

The design system is based on the Material Design guidelines. The components and design tokens are built from the specification in [Figma](https://www.figma.com/design/P7yuSMepVmqMjj9mePTac5/Design-System-Styles-(M3)?node-id=7-2&t=YswcnXTkZtVLoTiI-0).

## Installation

The SNF component library packages are distributed through the Azure Dev Ops artefacts. To access this registry update your .npmrc by running the following commands:
  
```bash
echo @snf:registry=https://pkgs.dev.azure.com/SNSF-CH/_packaging/SNSF-CH/npm/registry/ >> .npmrc
echo always-auth=true >> .npmrc
```

Developers need to authenticate to the Azure Dev Ops registry by running the following command:

```bash
npm install -g vsts-npm-auth --registry https://registry.npmjs.com --always-auth false
vsts-npm-auth -config .npmrc
```

To use this package install it via:

```bash
npm install @snf/design-system-components
```

Additionally, to set up the proper CSS variables, you need to import the provided `style.scss` file in your project.

```css
@import "@snf/design-system-components/style.scss";
```

To use specific functions/mixins in your project, you can import the `style.scss` file.

```css
@use "@snf/design-system-components/style.scss" as snf-library;

.my-component {
  color: snf-library.spacing(2);
}
```

### Angular

Add the following to the `styles` and `stylePreprocessorOptions` array in the `angular.json` or `project.json` file of your project.

```json
{
  "styles": [
    "node_modules/@snf/design-system-components/dist/style.scss"
  ],
  "stylePreprocessorOptions": {
    "includePaths": ["apps/grant-management/src/styles", "node_modules/@snf/design-system-components/dist"]
  }
}
```

## Usage

To use all components, you can import the `index.js` file in your project:

```ts
import '@snf/design-system-components';
```

If you want to lazy load the components where you need them, you can import the individual components:

```ts
import '@snf/design-system-components/src/components/button/button.component';
```

