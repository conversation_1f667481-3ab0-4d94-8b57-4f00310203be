@use "../../rootStyles/colors/color" as color;
@use "../../rootStyles/typographies/typography" as typography;
@use "../../rootStyles/spacings/spacing" as spacing;
@use "../../rootStyles/shapes/shape" as shape;
@use "../../rootStyles/elevations/elevation" as elevation;

.expansion-panel-wrapper {
  display: flex;
  flex-direction: column;
  gap: spacing.spacing(4);
  padding: spacing.spacing(4);
  background-color: color.color-scheme(surface);
  border-radius: shape.shape(medium);

  &.flat {
    background-color: transparent;
    padding: 0;
  }
}

.expansion-panel-info {
  display: flex;
  flex-direction: column;
  gap: spacing.spacing(2);

  h3 {
    @include typography.title-large();
    color: color.color-scheme(on-surface);
    margin: 0;
  }

  p {
    @include typography.body-medium();
    color: color.color-scheme(on-surface-variant);
    margin: 0;
  }

  .config-info {
    display: flex;
    flex-wrap: wrap;
    gap: spacing.spacing(3);
    margin-top: spacing.spacing(2);

    .config-item {
      @include typography.label-medium();
      color: color.color-scheme(on-surface-variant);
      padding: spacing.spacing(1) spacing.spacing(2);
      background-color: color.color-scheme(surface-container);
      border-radius: shape.shape(small);

      strong {
        color: color.color-scheme(primary);
        font-weight: var(--typeface-weight-medium);
      }
    }
  }
}

.expansion-panel-demo {
  // Angular Material expansion panel styling with SNF theme
  .snf-expansion-panel {
    box-shadow: elevation.elevation(1);
    border-radius: shape.shape(medium);
    overflow: hidden;

    .mat-expansion-panel {
      background-color: color.color-scheme(surface);
      border-radius: 0;
      box-shadow: none;
      border-bottom: 1px solid color.color-scheme(outline-variant);

      &:last-child {
        border-bottom: none;
        border-radius: 0 0 shape.shape(medium) shape.shape(medium);
      }

      &:first-child {
        border-radius: shape.shape(medium) shape.shape(medium) 0 0;
      }

      &:only-child {
        border-radius: shape.shape(medium);
      }

      .mat-expansion-panel-header {
        background-color: color.color-scheme(surface);
        color: color.color-scheme(on-surface);
        height: 70px;
        padding: 0 spacing.spacing(3);

        &:hover {
          background-color: color.color-scheme(surface-container);
        }

        &.mat-expanded {
          background-color: color.color-scheme(surface-container);
          height: 70px;
        }

        .mat-expansion-panel-header-title {
          @include typography.title-medium();
          color: color.color-scheme(on-surface);
        }

        .mat-expansion-panel-header-description {
          @include typography.body-medium();
          color: color.color-scheme(on-surface-variant);
        }

        .mat-expansion-indicator {
          &::after {
            color: color.color-scheme(on-surface-variant);
          }
        }
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          background-color: color.color-scheme(surface-container);
          padding: spacing.spacing(3);
          @include typography.body-medium();
          color: color.color-scheme(on-surface);
        }
      }

      &.mat-expansion-panel-spacing {
        margin: 0;
      }
    }
  }

  // Form styling within panels
  .panel-content {
    .form-example {
      display: flex;
      flex-direction: column;
      gap: spacing.spacing(3);
      margin-top: spacing.spacing(3);

      .form-field {
        display: flex;
        flex-direction: column;
        gap: spacing.spacing(1);

        label {
          @include typography.label-medium();
          color: color.color-scheme(on-surface);
          font-weight: var(--typeface-weight-medium);
        }

        input {
          padding: spacing.spacing(2) spacing.spacing(3);
          border: 1px solid color.color-scheme(outline);
          border-radius: shape.shape(small);
          background-color: color.color-scheme(surface);
          color: color.color-scheme(on-surface);
          @include typography.body-medium();

          &:focus {
            outline: 2px solid color.color-scheme(primary);
            outline-offset: -1px;
            border-color: color.color-scheme(primary);
          }

          &::placeholder {
            color: color.color-scheme(on-surface-variant);
          }
        }
      }
    }

    .settings-example {
      display: flex;
      flex-direction: column;
      gap: spacing.spacing(2);
      margin-top: spacing.spacing(3);

      .checkbox-label {
        display: flex;
        align-items: center;
        gap: spacing.spacing(2);
        @include typography.body-medium();
        color: color.color-scheme(on-surface);
        cursor: pointer;

        input[type="checkbox"] {
          width: 18px;
          height: 18px;
          accent-color: color.color-scheme(primary);
        }
      }
    }
  }
}

.code-example,
.scss-example {
  h4 {
    @include typography.title-small();
    color: color.color-scheme(on-surface);
    margin: 0 0 spacing.spacing(2) 0;
  }

  pre {
    background-color: color.color-scheme(surface-container-high);
    border: 1px solid color.color-scheme(outline-variant);
    border-radius: shape.shape(small);
    padding: spacing.spacing(3);
    overflow-x: auto;
    margin: 0;

    code {
      @include typography.body-small();
      color: color.color-scheme(on-surface);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      white-space: pre;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .expansion-panel-wrapper {
    padding: spacing.spacing(2);
    gap: spacing.spacing(3);
  }

  .config-info {
    .config-item {
      font-size: 0.875rem;
      padding: spacing.spacing(0.5) spacing.spacing(1.5);
    }
  }

  .expansion-panel-demo {
    .snf-expansion-panel {
      .mat-expansion-panel {
        .mat-expansion-panel-header {
          height: 60px;
          padding: 0 spacing.spacing(2);

          &.mat-expanded {
            height: 60px;
          }

          .mat-expansion-panel-header-title {
            @include typography.title-small();
          }

          .mat-expansion-panel-header-description {
            @include typography.body-small();
          }
        }

        .mat-expansion-panel-content {
          .mat-expansion-panel-body {
            padding: spacing.spacing(2);
          }
        }
      }
    }
  }
}
