# Angular Material Expansion Panel

The Angular Material Expansion Panel component provides a way to display expandable content sections with the SNF Design System theming applied. This component demonstrates how to integrate Angular Material components with your custom design tokens and styling.

## Overview

Expansion panels are useful for organizing content into collapsible sections, making interfaces cleaner and more navigable. They're commonly used in:

- Forms with multiple sections
- FAQ pages
- Settings panels
- Data entry workflows
- Documentation sections

## Features

- **Multiple Variants**: Single panel, accordion, and multi-expandable modes
- **Custom Theming**: Integrated with SNF Design System tokens
- **Responsive Design**: Adapts to different screen sizes
- **Accessibility**: Full keyboard navigation and screen reader support
- **Flexible Content**: Supports any HTML content within panels

## Installation & Setup

### 1. Install Dependencies

```bash
npm install @angular/material @angular/cdk @snf/design-system-components
```

### 2. Import Angular Material Modules

```typescript
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';

@NgModule({
  imports: [
    MatExpansionModule,
    MatIconModule,
    // ... other imports
  ],
})
export class AppModule { }
```

### 3. Apply SNF Theme

```scss
@use '@angular/material' as mat;
@use '@snf/design-system-components/style' as snf;

// Include the SNF theme
html {
  @include mat.core-theme($snf-theme);
  @include mat.all-component-themes($snf-theme);
  @include snf.all-responsive-styles();
}
```

## Usage Examples

### Basic Expansion Panel

```html
<mat-accordion class="snf-expansion-panel">
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>Panel Title</mat-panel-title>
      <mat-panel-description>
        Panel description
      </mat-panel-description>
    </mat-expansion-panel-header>
    <div class="panel-content">
      <p>Panel content goes here...</p>
    </div>
  </mat-expansion-panel>
</mat-accordion>
```

### Multi-Expandable Accordion

```html
<mat-accordion class="snf-expansion-panel" multi="true">
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>First Panel</mat-panel-title>
    </mat-expansion-panel-header>
    <div class="panel-content">Content 1</div>
  </mat-expansion-panel>
  
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>Second Panel</mat-panel-title>
    </mat-expansion-panel-header>
    <div class="panel-content">Content 2</div>
  </mat-expansion-panel>
</mat-accordion>
```

### With Form Content

```html
<mat-accordion class="snf-expansion-panel">
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>User Information</mat-panel-title>
      <mat-panel-description>Enter your details</mat-panel-description>
    </mat-expansion-panel-header>
    <div class="panel-content">
      <form>
        <mat-form-field>
          <mat-label>Name</mat-label>
          <input matInput placeholder="Enter your name">
        </mat-form-field>
        <mat-form-field>
          <mat-label>Email</mat-label>
          <input matInput type="email" placeholder="Enter your email">
        </mat-form-field>
      </form>
    </div>
  </mat-expansion-panel>
</mat-accordion>
```

## Styling with SNF Design System

### SCSS Integration

```scss
@use '@snf/design-system-components/style' as snf;

.snf-expansion-panel {
  // Apply SNF theme overrides
  .mat-expansion-panel-header {
    background-color: var(--color-surface);
    color: var(--color-on-surface);
    height: 70px; // SNF standard height
    
    &:hover {
      background-color: var(--color-surface-container);
    }
  }
  
  .mat-expansion-panel-body {
    background-color: var(--color-surface-container);
    padding: var(--spacing-3);
  }
  
  .mat-panel-title {
    @include snf.title-medium();
  }
  
  .mat-panel-description {
    @include snf.body-medium();
    color: var(--color-on-surface-variant);
  }
}
```

### Design Token Usage

The component uses the following SNF design tokens:

- **Colors**: `--color-surface`, `--color-on-surface`, `--color-surface-container`
- **Typography**: `--typescale-title-medium-*`, `--typescale-body-medium-*`
- **Spacing**: `--spacing-*` tokens for consistent padding and margins
- **Shapes**: `--shape-medium` for border radius
- **Elevations**: `--elevation-1` for subtle shadows

## Accessibility

The expansion panel component includes:

- **Keyboard Navigation**: Tab, Enter, and Space key support
- **ARIA Labels**: Proper labeling for screen readers
- **Focus Management**: Clear focus indicators
- **State Announcements**: Expansion state changes are announced

## Best Practices

1. **Content Organization**: Group related content logically
2. **Clear Titles**: Use descriptive panel titles
3. **Progressive Disclosure**: Show essential information first
4. **Consistent Styling**: Apply the `snf-expansion-panel` class for theme consistency
5. **Mobile Considerations**: Test on mobile devices for touch interactions

## Variants

- **Single**: Only one panel can be expanded at a time (default)
- **Accordion**: Traditional accordion behavior
- **Multi-expandable**: Multiple panels can be open simultaneously

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Related Components

- [Angular Material Accordion](https://material.angular.io/components/expansion/overview)
- [SNF Callout Component](../callout/callout.md)
- [SNF Info Box Component](../infobox/info-box.md)
