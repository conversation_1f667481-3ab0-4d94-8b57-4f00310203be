import { html, unsafeCSS } from 'lit';
import { property } from 'lit/decorators.js';
import BaseElement from '../../internals/baseElement/baseElement';
import styles from './expansion-panel.scss?inline';
import registerElement from '../../components/registerElement';

export const expansionPanelVariants = [
  'single',
  'accordion',
  'multi-expandable'
] as const;
export type ExpansionPanelVariant = typeof expansionPanelVariants[number];

export interface ExpansionPanelData {
  title: string;
  description?: string;
  content: string;
  expanded?: boolean;
  disabled?: boolean;
}

/**
 * Angular Material Expansion Panel Wrapper Component
 * 
 * This component demonstrates how to use Angular Material expansion panels
 * with the SNF Design System theming and styling.
 * 
 * @slot - Pass the expansion panel content
 * @variant - The type of expansion panel behavior (single, accordion, multi-expandable)
 * @panels - Array of panel data to display
 * @hideToggle - Whether to hide the toggle indicator
 * @displayMode - Display mode for the expansion panel (default, flat)
 */
export default class ExpansionPanel extends BaseElement {
  static override styles = [
    BaseElement.globalStyles,
    unsafeCSS(styles),
  ];

  @property({ type: String })
  public variant: ExpansionPanelVariant = 'single';

  @property({ type: Array })
  public panels: ExpansionPanelData[] = [];

  @property({ type: Boolean })
  public hideToggle = false;

  @property({ type: String })
  public displayMode: 'default' | 'flat' = 'default';

  @property({ type: Boolean })
  public multi = false;

  override render() {
    return html`
      <div class="expansion-panel-wrapper ${this.displayMode}">
        <div class="expansion-panel-info">
          <h3>Angular Material Expansion Panel</h3>
          <p>This component demonstrates Angular Material expansion panels with SNF Design System theming.</p>
          <div class="config-info">
            <span class="config-item">Variant: <strong>${this.variant}</strong></span>
            <span class="config-item">Display Mode: <strong>${this.displayMode}</strong></span>
            <span class="config-item">Multi-expand: <strong>${this.multi ? 'Yes' : 'No'}</strong></span>
          </div>
        </div>

        <div class="expansion-panel-demo">
          ${this.renderExpansionPanels()}
        </div>

        <div class="code-example">
          <h4>Angular Template Example:</h4>
          <pre><code>${this.getAngularTemplateExample()}</code></pre>
        </div>

        <div class="scss-example">
          <h4>SCSS Theme Integration:</h4>
          <pre><code>${this.getScssExample()}</code></pre>
        </div>
      </div>
    `;
  }

  private renderExpansionPanels() {
    if (this.panels.length === 0) {
      return this.renderDefaultPanels();
    }

    return html`
      <mat-accordion 
        class="snf-expansion-panel"
        ?multi="${this.multi}"
        ?hideToggle="${this.hideToggle}"
        displayMode="${this.displayMode}"
      >
        ${this.panels.map((panel, index) => html`
          <mat-expansion-panel 
            ?expanded="${panel.expanded}"
            ?disabled="${panel.disabled}"
          >
            <mat-expansion-panel-header>
              <mat-panel-title>${panel.title}</mat-panel-title>
              ${panel.description ? html`
                <mat-panel-description>${panel.description}</mat-panel-description>
              ` : ''}
            </mat-expansion-panel-header>
            <div class="panel-content">
              ${panel.content}
            </div>
          </mat-expansion-panel>
        `)}
      </mat-accordion>
    `;
  }

  private renderDefaultPanels() {
    return html`
      <mat-accordion class="snf-expansion-panel" ?multi="${this.multi}">
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>Personal Information</mat-panel-title>
            <mat-panel-description>
              Enter your personal details
            </mat-panel-description>
          </mat-expansion-panel-header>
          <div class="panel-content">
            <p>This is where you would add form fields for personal information.</p>
            <div class="form-example">
              <div class="form-field">
                <label>First Name</label>
                <input type="text" placeholder="Enter first name" />
              </div>
              <div class="form-field">
                <label>Last Name</label>
                <input type="text" placeholder="Enter last name" />
              </div>
            </div>
          </div>
        </mat-expansion-panel>

        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>Contact Information</mat-panel-title>
            <mat-panel-description>
              Phone and email details
            </mat-panel-description>
          </mat-expansion-panel-header>
          <div class="panel-content">
            <p>Contact information form fields would go here.</p>
            <div class="form-example">
              <div class="form-field">
                <label>Email</label>
                <input type="email" placeholder="Enter email address" />
              </div>
              <div class="form-field">
                <label>Phone</label>
                <input type="tel" placeholder="Enter phone number" />
              </div>
            </div>
          </div>
        </mat-expansion-panel>

        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>Additional Settings</mat-panel-title>
            <mat-panel-description>
              Optional configuration
            </mat-panel-description>
          </mat-expansion-panel-header>
          <div class="panel-content">
            <p>Additional settings and preferences.</p>
            <div class="settings-example">
              <label class="checkbox-label">
                <input type="checkbox" /> Enable notifications
              </label>
              <label class="checkbox-label">
                <input type="checkbox" /> Auto-save changes
              </label>
            </div>
          </div>
        </mat-expansion-panel>
      </mat-accordion>
    `;
  }

  private getAngularTemplateExample(): string {
    return `<mat-accordion class="snf-expansion-panel" multi="false">
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>Panel Title</mat-panel-title>
      <mat-panel-description>
        Panel description
      </mat-panel-description>
    </mat-expansion-panel-header>
    <div class="panel-content">
      <!-- Your content here -->
    </div>
  </mat-expansion-panel>
</mat-accordion>`;
  }

  private getScssExample(): string {
    return `@use '@angular/material' as mat;
@use '@snf/design-system-components/style' as snf;

// Apply SNF theme to expansion panels
.snf-expansion-panel {
  @include mat.expansion-panel-theme($snf-theme);
  
  // Custom overrides using design tokens
  .mat-expansion-panel-header {
    background-color: var(--color-surface);
    color: var(--color-on-surface);
  }
  
  .mat-expansion-panel-body {
    background-color: var(--color-surface-container);
  }
}`;
  }
}

registerElement('snf-expansion-panel', ExpansionPanel);

declare global {
  interface HTMLElementTagNameMap {
    'snf-expansion-panel': ExpansionPanel;
  }
}
