import ExpansionPanel, { expansionPanelVariants, ExpansionPanelData } from "./expansion-panel.component";
import docs from './expansion-panel.md?raw';
import { html } from 'lit-html';
import { Meta, StoryFn, StoryObj, WebComponentsRenderer } from '@storybook/web-components';
import { unsafeHTML } from 'lit-html/directives/unsafe-html.js';
import { withActions } from '@storybook/addon-actions/decorator';
import { ifDefined } from "lit-html/directives/if-defined.js";

const meta: Meta<ExpansionPanel> = {
  title: 'Angular Components/Expansion Panel',
  component: 'snf-expansion-panel',
  argTypes: {
    variant: { 
      control: 'select', 
      options: expansionPanelVariants,
      description: 'The behavior type of the expansion panel'
    },
    displayMode: { 
      control: 'select', 
      options: ['default', 'flat'],
      description: 'Display mode for the expansion panel'
    },
    multi: { 
      control: 'boolean',
      description: 'Allow multiple panels to be expanded simultaneously'
    },
    hideToggle: { 
      control: 'boolean',
      description: 'Hide the toggle indicator'
    },
    panels: {
      control: 'object',
      description: 'Array of panel data to display'
    }
  },
  parameters: {
    docs: {
      description: {
        component: docs,
      },
    },
    layout: 'padded',
  },
  decorators: [withActions<WebComponentsRenderer>],
};
export default meta;

const Template: StoryFn<ExpansionPanel> = ({
  variant,
  displayMode,
  multi,
  hideToggle,
  panels
}) => html`
  <snf-expansion-panel
    variant=${ifDefined(variant)}
    displayMode=${ifDefined(displayMode)}
    ?multi=${multi}
    ?hideToggle=${hideToggle}
    .panels=${panels || []}
  ></snf-expansion-panel>
`;

export const Default: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'single',
    displayMode: 'default',
    multi: false,
    hideToggle: false,
  },
};

export const Accordion: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'accordion',
    displayMode: 'default',
    multi: false,
    hideToggle: false,
  },
};

export const MultiExpandable: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'multi-expandable',
    displayMode: 'default',
    multi: true,
    hideToggle: false,
  },
};

export const FlatDisplay: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'single',
    displayMode: 'flat',
    multi: false,
    hideToggle: false,
  },
};

export const HiddenToggle: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'single',
    displayMode: 'default',
    multi: false,
    hideToggle: true,
  },
};

export const CustomPanels: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'single',
    displayMode: 'default',
    multi: false,
    hideToggle: false,
    panels: [
      {
        title: 'Project Overview',
        description: 'Basic project information',
        content: `
          <div>
            <h4>Project Details</h4>
            <p>This project demonstrates the integration of Angular Material components with the SNF Design System.</p>
            <ul>
              <li>Custom theming applied</li>
              <li>Design tokens integration</li>
              <li>Responsive design</li>
            </ul>
          </div>
        `,
        expanded: true
      },
      {
        title: 'Technical Specifications',
        description: 'Implementation details',
        content: `
          <div>
            <h4>Technologies Used</h4>
            <p>The following technologies are used in this implementation:</p>
            <ul>
              <li>Angular Material 17+</li>
              <li>SNF Design System</li>
              <li>SCSS with design tokens</li>
              <li>Lit web components</li>
            </ul>
          </div>
        `
      },
      {
        title: 'Usage Guidelines',
        description: 'How to implement in your project',
        content: `
          <div>
            <h4>Implementation Steps</h4>
            <ol>
              <li>Install Angular Material and SNF Design System</li>
              <li>Import the required modules</li>
              <li>Apply the SNF theme configuration</li>
              <li>Use the expansion panel components</li>
            </ol>
            <p><strong>Note:</strong> Make sure to follow the theming guidelines for consistent styling.</p>
          </div>
        `,
        disabled: false
      }
    ] as ExpansionPanelData[]
  },
};

export const WithFormContent: StoryObj<ExpansionPanel> = {
  render: Template,
  args: {
    variant: 'accordion',
    displayMode: 'default',
    multi: false,
    hideToggle: false,
    panels: [
      {
        title: 'User Information',
        description: 'Personal details form',
        content: `
          <form>
            <div style="display: flex; flex-direction: column; gap: 16px;">
              <div>
                <label for="firstName" style="display: block; margin-bottom: 4px; font-weight: 500;">First Name</label>
                <input id="firstName" type="text" placeholder="Enter your first name" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 4px;" />
              </div>
              <div>
                <label for="lastName" style="display: block; margin-bottom: 4px; font-weight: 500;">Last Name</label>
                <input id="lastName" type="text" placeholder="Enter your last name" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 4px;" />
              </div>
              <div>
                <label for="email" style="display: block; margin-bottom: 4px; font-weight: 500;">Email</label>
                <input id="email" type="email" placeholder="Enter your email" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 4px;" />
              </div>
            </div>
          </form>
        `,
        expanded: true
      },
      {
        title: 'Address Information',
        description: 'Location details',
        content: `
          <form>
            <div style="display: flex; flex-direction: column; gap: 16px;">
              <div>
                <label for="street" style="display: block; margin-bottom: 4px; font-weight: 500;">Street Address</label>
                <input id="street" type="text" placeholder="Enter street address" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 4px;" />
              </div>
              <div style="display: flex; gap: 16px;">
                <div style="flex: 1;">
                  <label for="city" style="display: block; margin-bottom: 4px; font-weight: 500;">City</label>
                  <input id="city" type="text" placeholder="City" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 4px;" />
                </div>
                <div style="flex: 1;">
                  <label for="zip" style="display: block; margin-bottom: 4px; font-weight: 500;">ZIP Code</label>
                  <input id="zip" type="text" placeholder="ZIP" style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 4px;" />
                </div>
              </div>
            </div>
          </form>
        `
      },
      {
        title: 'Preferences',
        description: 'User preferences and settings',
        content: `
          <div style="display: flex; flex-direction: column; gap: 12px;">
            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
              <input type="checkbox" style="width: 16px; height: 16px;" />
              <span>Receive email notifications</span>
            </label>
            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
              <input type="checkbox" style="width: 16px; height: 16px;" />
              <span>Enable two-factor authentication</span>
            </label>
            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
              <input type="checkbox" style="width: 16px; height: 16px;" />
              <span>Share data for analytics</span>
            </label>
          </div>
        `
      }
    ] as ExpansionPanelData[]
  },
};
