import { expect, fixture, html } from '@open-wc/testing-helpers';
import { ExpansionPanel } from './expansion-panel.component';
import './expansion-panel.component';

describe('ExpansionPanel', () => {
  let element: ExpansionPanel;

  beforeEach(async () => {
    element = await fixture(html`<snf-expansion-panel></snf-expansion-panel>`);
  });

  it('should render with default properties', () => {
    expect(element).to.exist;
    expect(element.variant).to.equal('single');
    expect(element.displayMode).to.equal('default');
    expect(element.multi).to.be.false;
    expect(element.hideToggle).to.be.false;
    expect(element.panels).to.deep.equal([]);
  });

  it('should render expansion panel wrapper', () => {
    const wrapper = element.shadowRoot?.querySelector('.expansion-panel-wrapper');
    expect(wrapper).to.exist;
  });

  it('should render expansion panel info section', () => {
    const info = element.shadowRoot?.querySelector('.expansion-panel-info');
    expect(info).to.exist;
    
    const title = info?.querySelector('h3');
    expect(title?.textContent).to.equal('Angular Material Expansion Panel');
  });

  it('should render demo section', () => {
    const demo = element.shadowRoot?.querySelector('.expansion-panel-demo');
    expect(demo).to.exist;
  });

  it('should render code examples', () => {
    const codeExample = element.shadowRoot?.querySelector('.code-example');
    const scssExample = element.shadowRoot?.querySelector('.scss-example');
    
    expect(codeExample).to.exist;
    expect(scssExample).to.exist;
  });

  it('should update variant property', async () => {
    element.variant = 'accordion';
    await element.updateComplete;
    
    expect(element.variant).to.equal('accordion');
    
    const configInfo = element.shadowRoot?.querySelector('.config-info');
    expect(configInfo?.textContent).to.include('accordion');
  });

  it('should update displayMode property', async () => {
    element.displayMode = 'flat';
    await element.updateComplete;
    
    expect(element.displayMode).to.equal('flat');
    
    const wrapper = element.shadowRoot?.querySelector('.expansion-panel-wrapper');
    expect(wrapper?.classList.contains('flat')).to.be.true;
  });

  it('should update multi property', async () => {
    element.multi = true;
    await element.updateComplete;
    
    expect(element.multi).to.be.true;
    
    const configInfo = element.shadowRoot?.querySelector('.config-info');
    expect(configInfo?.textContent).to.include('Yes');
  });

  it('should render custom panels when provided', async () => {
    const customPanels = [
      {
        title: 'Test Panel 1',
        description: 'Test Description 1',
        content: 'Test Content 1',
        expanded: true
      },
      {
        title: 'Test Panel 2',
        content: 'Test Content 2',
        disabled: true
      }
    ];

    element.panels = customPanels;
    await element.updateComplete;

    expect(element.panels).to.deep.equal(customPanels);
  });

  it('should render default panels when no custom panels provided', async () => {
    element.panels = [];
    await element.updateComplete;

    // Should render the default panels
    const demo = element.shadowRoot?.querySelector('.expansion-panel-demo');
    expect(demo).to.exist;
  });

  it('should generate correct Angular template example', () => {
    const templateExample = (element as any).getAngularTemplateExample();
    
    expect(templateExample).to.include('<mat-accordion');
    expect(templateExample).to.include('class="snf-expansion-panel"');
    expect(templateExample).to.include('<mat-expansion-panel>');
    expect(templateExample).to.include('<mat-panel-title>');
    expect(templateExample).to.include('<mat-panel-description>');
  });

  it('should generate correct SCSS example', () => {
    const scssExample = (element as any).getScssExample();
    
    expect(scssExample).to.include('@use \'@angular/material\' as mat;');
    expect(scssExample).to.include('@use \'@snf/design-system-components/style\' as snf;');
    expect(scssExample).to.include('.snf-expansion-panel');
    expect(scssExample).to.include('var(--color-surface)');
  });

  it('should handle hideToggle property', async () => {
    element.hideToggle = true;
    await element.updateComplete;
    
    expect(element.hideToggle).to.be.true;
  });

  it('should be accessible', async () => {
    await expect(element).to.be.accessible();
  });

  describe('Panel Data Interface', () => {
    it('should handle panel data with all properties', async () => {
      const panelData = {
        title: 'Test Title',
        description: 'Test Description',
        content: 'Test Content',
        expanded: true,
        disabled: false
      };

      element.panels = [panelData];
      await element.updateComplete;

      expect(element.panels[0]).to.deep.equal(panelData);
    });

    it('should handle panel data with minimal properties', async () => {
      const panelData = {
        title: 'Minimal Panel',
        content: 'Minimal Content'
      };

      element.panels = [panelData];
      await element.updateComplete;

      expect(element.panels[0].title).to.equal('Minimal Panel');
      expect(element.panels[0].content).to.equal('Minimal Content');
      expect(element.panels[0].description).to.be.undefined;
      expect(element.panels[0].expanded).to.be.undefined;
      expect(element.panels[0].disabled).to.be.undefined;
    });
  });

  describe('Responsive Behavior', () => {
    it('should apply responsive classes', () => {
      const wrapper = element.shadowRoot?.querySelector('.expansion-panel-wrapper');
      expect(wrapper).to.exist;
      
      // The responsive behavior is handled via CSS media queries
      // We can test that the base structure is in place
      expect(wrapper?.classList.contains('expansion-panel-wrapper')).to.be.true;
    });
  });

  describe('Theme Integration', () => {
    it('should apply SNF theme classes', () => {
      const demo = element.shadowRoot?.querySelector('.expansion-panel-demo');
      expect(demo).to.exist;
      
      // The theme integration is primarily handled via CSS
      // We can verify the structure is in place for theming
      expect(demo?.querySelector('.snf-expansion-panel')).to.exist;
    });
  });
});
