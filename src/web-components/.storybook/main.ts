import { dirname, join } from 'path';
import { mergeConfig } from 'vite';
import svg from 'vite-plugin-svgo';
import turbosnap from 'vite-plugin-turbosnap';

// Convert __dirname for ESM
const basePath = '/storybook/design-system/';

export default {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: [
    getAbsolutePath('@storybook/addon-links'),
    getAbsolutePath('@storybook/addon-essentials'),
    getAbsolutePath('@storybook/addon-a11y'),
    getAbsolutePath('@chromatic-com/storybook'),
    getAbsolutePath('@storybook/addon-mdx-gfm'),
    '@chromatic-com/storybook'
  ],
  framework: {
    name: getAbsolutePath('@storybook/web-components-vite'),
    options: {},
  },
  core: {
    channelOptions: { allowFunction: false, maxDepth: 10 }
  },
  async viteFinal(config, { configType }) {
    return mergeConfig(config, {
      plugins: [
        ...(configType === 'PRODUCTION' ? [turbosnap({ rootDir: config.root ?? process.cwd() })] : []),
        svg({
          plugins: [
            {
              name: 'preset-default',
              params: {
                overrides: {
                  convertColors: { currentColor: true },
                  removeViewBox: false,
                },
              },
            },
            { name: 'removeDimensions' },
          ],
        }),
      ],
    });
  },
  docs: {},
};

function getAbsolutePath(value: string) {
  return dirname(require.resolve(join(value, 'package.json')));
}
