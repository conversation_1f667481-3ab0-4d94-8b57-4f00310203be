{"name": "@snf/design-system", "private": true, "version": "1.0.0", "description": "Root project for the web-components monorepo", "scripts": {"check": "npm run check --workspaces --if-present", "lint": "npm run lint --workspaces --if-present", "lint:fix": "npm run lint:fix --workspaces --if-present", "test": "npm run test --workspaces --if-present", "build": "npm run build --workspaces --if-present", "link": "npm run link --workspaces --if-present", "build:link": "npm run build:link --workspaces --if-present", "storybook": "npm run storybook --workspaces --if-present", "analyze": "npm run analyze --workspaces --if-present", "link:web-components": "npm run --workspace web-components link", "link:angular": "npm run --workspace angular-wrapper link", "build:link:web-components": "npm run --workspace web-components build:link", "build:link:angular": "npm run --workspace angular-wrapper build:link", "format": "npm run --workspace angular-wrapper format", "format:check": "npm run --workspace angular-wrapper format:check", "version:major": "npm version major --workspaces", "version:minor": "npm version minor --workspaces", "version:patch": "npm version patch --workspaces"}, "workspaces": ["tailwind-preset", "web-components", "angular-wrapper"], "repository": {"type": "git", "url": "https://dev.azure.com/SNSF-CH/SNF%20Portal/_git/library-SNF-DesignSystem"}, "author": "", "license": "ISC", "devDependencies": {"eslint-plugin-import": "2.32.0"}}