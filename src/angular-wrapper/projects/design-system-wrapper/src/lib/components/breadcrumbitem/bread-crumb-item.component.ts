import { Component, forwardRef, Host, Input, Optional } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NAVIGABLE_COMPONENT_TOKEN, NavigableComponent } from '../../models';

@Component({
  selector: 'snf-library-bread-crumb-item',
  templateUrl: './bread-crumb-item.component.html',
  providers: [
    {
      provide: NAVIGABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => BreadCrumbItemComponent),
    },
  ],
  standalone: false,
})
export class BreadCrumbItemComponent implements NavigableComponent {
  @Input() public href: string = '#';
  @Input() public type: string = 'link';

  constructor(@Optional() @Host() public routerLink: RouterLink) {}
}
