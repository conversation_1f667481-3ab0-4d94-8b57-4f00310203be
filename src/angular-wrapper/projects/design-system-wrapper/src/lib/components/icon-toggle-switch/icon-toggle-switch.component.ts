import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ToggleValue } from '@snf/design-system-components/src/internals/utils/toggle-value';

@Component({
  selector: 'snf-library-icon-toggle-switch',
  templateUrl: './icon-toggle-switch.component.html',
  standalone: false,
})
export class IconToggleSwitchComponent {
  @Input() public toggleValue: ToggleValue = 'none';
  @Input() public disabled = false;

  @Output() public toggleOn = new EventEmitter<boolean>();

  public handleToggleOn(event: Event): void {
    event.stopPropagation();
    const customEvent = event as CustomEvent<{ value: boolean }>;
    this.toggleOn.emit(customEvent.detail?.value ?? false);
  }
}
