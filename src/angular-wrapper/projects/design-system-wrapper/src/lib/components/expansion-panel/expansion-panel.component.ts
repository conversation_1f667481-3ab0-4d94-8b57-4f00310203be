import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Theme } from '@snf/design-system-components/src/internals/utils/theme';

@Component({
  selector: 'snf-library-expansion-panel',
  templateUrl: './expansion-panel.component.html',
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
  standalone: false,
})
export class ExpansionPanelComponent {
  @Input() public expanded: boolean = false;
  @Input() public hasError: boolean = false;
  @Input() public disabled: boolean = false;
  @Input() public theme: Theme = 'light';
  @Output() public toggleExpansionPanel = new EventEmitter<boolean>();

  public handleToggle(event: Event): void {
    event.stopPropagation();
    const customEvent = event as CustomEvent<{ value: boolean }>;
    this.toggleExpansionPanel.emit(customEvent.detail.value);
  }
}
