import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  ActionType,
  InfoBoxSizes,
} from '@snf/design-system-components/src/components/infobox/info-box.component';
import { ColorSchemes } from '@snf/design-system-components/src/internals/utils/color-scheme';

@Component({
  selector: 'snf-library-info-box',
  styleUrls: ['./info-box.component.scss'],
  template:
    '<snf-info-box [showIcon]="showIcon" [size]="size" [color]="color" [actionType]="actionType" (actionClick)="onClick($event)"><ng-content></ng-content></snf-info-box>',
  standalone: false,
})
export class InfoBoxComponent {
  @Input() public showIcon = false;
  @Input() public actionType: ActionType = 'none';
  @Input() public size: InfoBoxSizes = 'large';
  @Input() public color: ColorSchemes = 'neutral';

  @Output() public clicked = new EventEmitter<Event>();

  public onClick(event: Event): void {
    this.clicked.emit(event);
  }
}
