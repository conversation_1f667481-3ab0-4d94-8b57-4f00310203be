import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  IconSizes,
  IconStyles,
} from '@snf/design-system-components/src/components/icon/icon.component';

@Component({
  selector: 'snf-library-icon-button',
  templateUrl: './icon-button.component.html',
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
  standalone: false,
})
export class IconButtonComponent {
  @Input() public iconStyle: IconStyles = 'filled';
  @Input() public iconSize: IconSizes = 'small';
  @Input() public disabled: boolean = false;

  @Output() public iconClick = new EventEmitter<void>();

  public handleClick(event: Event): void {
    event.stopPropagation();
    this.iconClick.emit();
  }
}
