import { Component, forwardRef, Input } from '@angular/core';
import { LinkButtonTargets } from '@snf/design-system-components/src/components/buttons/link-button/link-button.component';
import { NAVIGABLE_COMPONENT_TOKEN, NavigableComponent } from '../../../models';
import { ButtonWrapperBase } from '../../../utils/button-wrapper';

@Component({
  selector: 'snf-library-link-button',
  templateUrl: './link-button.component.html',
  providers: [
    {
      provide: NAVIGABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => LinkButtonComponent),
    },
  ],
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
  standalone: false,
})
export class LinkButtonComponent extends ButtonWrapperBase implements NavigableComponent {
  @Input() public href: string = '';
  @Input() public underlined: boolean = true;
  @Input() public target: LinkButtonTargets | undefined;
}
