import { Component, Input } from '@angular/core';

@Component({
  selector: 'snf-library-side-nav-item-sub-nav',
  template:
    '<snf-side-nav-item-sub-nav slot="submenu" [expanded]="this.expanded"><ng-content></ng-content></snf-side-nav-item-sub-nav>',
  styles: [
    `
      :host {
        display: block;
        line-height: 0;
        font-size: 0;
      }
    `,
  ],
  standalone: false,
})
export class SideNavItemSubNavComponent {
  @Input() public expanded: boolean = false;
}
