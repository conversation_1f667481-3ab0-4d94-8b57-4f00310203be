import { Component, Input } from '@angular/core';
import {
  FieldType,
  ReadOnlyBoxSizes,
} from '@snf/design-system-components/src/components/readonly-field/readonly-field.component';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'snf-library-readonly-field',
  templateUrl: './readonly-field.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: ReadOnlyFieldComponent,
    },
  ],
  standalone: false,
})
export class ReadOnlyFieldComponent implements ControlValueAccessor {
  @Input() public label: string | null = null;
  @Input() public hint: string | null = null;
  @Input() public size: ReadOnlyBoxSizes = 'large';
  @Input() public fieldType: FieldType = 'multi-line';

  @Input() public value: string | null = null;

  protected disabled = false;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private onChange = (value: string): void => {};

  private onTouched = (): void => {};

  public writeValue(value: string): void {
    this.value = value;
  }
  public registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  public registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  public setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
