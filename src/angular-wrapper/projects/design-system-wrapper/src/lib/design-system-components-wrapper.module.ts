import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import '@snf/design-system-components/src/components/callout/callout.component';
import '@snf/design-system-components/src/components/breadcrumb/bread-crumb.component';
import '@snf/design-system-components/src/components/breadcrumb/breadcrumbitem/bread-crumb-item.component';
import '@snf/design-system-components/src/components/sidenav/side-nav.component';
import '@snf/design-system-components/src/components/sidenav/sidenavitem/side-nav-item.component';
import '@snf/design-system-components/src/components/sidenav/sidenavitem/sidenavitemcontent/side-nav-item-content.component';
import '@snf/design-system-components/src/components/sidenav/sidenavitem/sidenavitemicon/side-nav-item-icon.component';
import '@snf/design-system-components/src/components/sidenav/sidenavitem/sidenavitemsubnav/side-nav-item-sub-nav.component';
import '@snf/design-system-components/src/components/buttons/link-button/link-button.component';
import '@snf/design-system-components/src/components/buttons/select-button/select-button.component';
import '@snf/design-system-components/src/components/icon/icon.component';
import '@snf/design-system-components/src/components/icon-toggle-switch/icon-toggle-switch.component';
import '@snf/design-system-components/src/components/readonly-field/readonly-field.component';
import '@snf/design-system-components/src/components/readonly-checkbox/readonly-checkbox.component';
import '@snf/design-system-components/src/components/readonly-radio/readonly-radio.component';
import '@snf/design-system-components/src/components/badges/statusbadge/status-badge.component';
import '@snf/design-system-components/src/components/breadcrumb/bread-crumb.component';
import '@snf/design-system-components/src/components/breadcrumb/breadcrumbitem/bread-crumb-item.component';
import '@snf/design-system-components/src/components/text-toggle-switch/text-toggle-switch.component';
import '@snf/design-system-components/src/components/infobox/info-box.component';
import '@snf/design-system-components/src/components/verification-frame/verification-frame.component';
import '@snf/design-system-components/src/components/avatar/avatar.component';
import '@snf/design-system-components/src/components/avatar/avatar-list/avatar-list.component';
import '@snf/design-system-components/src/components/globalbanner/global-banner.component';
import '@snf/design-system-components/src/components/input/search-bar/search-bar.component';
import '@snf/design-system-components/src/components/buttons/icon-button/icon-button.component';

import { CalloutComponent } from './components';
import { BreadCrumbComponent } from './components';
import { BreadCrumbItemComponent } from './components';
import { LinkButtonComponent } from './components';
import { SelectButtonComponent } from './components';
import { IconComponent } from './components';
import { IconToggleSwitchComponent } from './components';
import { InfoBoxComponent } from './components';
import { ReadOnlyFieldComponent } from './components';
import { ReadonlyCheckboxComponent } from './components';
import { ReadonlyRadioComponent } from './components';
import { SideNavComponent } from './components';
import { SideNavItemComponent } from './components';
import { SideNavItemContentComponent } from './components';
import { SideNavItemIconComponent } from './components';
import { SideNavItemSubNavComponent } from './components';
import { StatusBadgeComponent } from './components';
import { TextToggleSwitchComponent } from './components';
import { VerificationFrameComponent } from './components';
import { GlobalBannerComponent } from './components';
import { SearchBarComponent } from './components';
import { AvatarComponent } from './components';
import { AvatarListComponent } from './components';
import { IconButtonComponent } from './components/buttons/icon-button/icon-button.component';

const COMPONENTS = [
  CalloutComponent,
  BreadCrumbComponent,
  BreadCrumbItemComponent,
  LinkButtonComponent,
  SelectButtonComponent,
  IconComponent,
  IconToggleSwitchComponent,
  InfoBoxComponent,
  ReadOnlyFieldComponent,
  ReadonlyCheckboxComponent,
  ReadonlyRadioComponent,
  SideNavComponent,
  SideNavItemComponent,
  SideNavItemContentComponent,
  SideNavItemIconComponent,
  SideNavItemSubNavComponent,
  StatusBadgeComponent,
  TextToggleSwitchComponent,
  VerificationFrameComponent,
  GlobalBannerComponent,
  SearchBarComponent,
  AvatarComponent,
  AvatarListComponent,
  IconButtonComponent,
];

@NgModule({
  declarations: [...COMPONENTS],
  exports: [...COMPONENTS],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DesignSystemComponentsWrapperModule {}
