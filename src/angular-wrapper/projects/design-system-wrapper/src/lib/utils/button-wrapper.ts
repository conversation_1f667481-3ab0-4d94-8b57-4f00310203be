import { Input, Directive, Output, EventEmitter } from '@angular/core';

@Directive()
export abstract class ButtonWrapperBase {
  @Input() public disabled = false;
  @Output() public clicked = new EventEmitter<MouseEvent>();
  @Output() public keyboardDown = new EventEmitter<KeyboardEvent>();

  protected preventClickWhenDisabled(event: MouseEvent): void {
    if (this.disabled) {
      return;
    }

    this.clicked.emit(event);
  }

  protected preventKeyboardDownWhenDisabled(event: KeyboardEvent): void {
    if (event.key !== 'Enter' && event.key !== ' ') {
      return;
    }

    if (this.disabled) {
      return;
    }

    this.keyboardDown.emit(event);
  }
}
