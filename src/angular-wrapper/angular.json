{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"design-system-wrapper": {"projectType": "library", "root": "projects/design-system-wrapper", "sourceRoot": "projects/design-system-wrapper/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/design-system-wrapper/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/design-system-wrapper/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/design-system-wrapper/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/design-system-wrapper/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}}, "cli": {"analytics": false}}