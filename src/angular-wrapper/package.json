{"name": "angular-wrapper", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build design-system-wrapper", "watch": "ng build --watch --configuration development", "link": "cd dist/design-system-wrapper && npm link", "build:link": "npm run build && npm run link", "lint": "eslint \"projects/**/*.ts\" --config eslint.config.js", "lint:fix": "eslint \"projects/**/*.ts\" --fix --config eslint.config.js", "format": "prettier --write \"projects/**/*.{ts,html,scss,json}\"", "format:check": "prettier --check \"projects/**/*.{ts,html,scss,json}\""}, "private": true, "type": "module", "dependencies": {"@angular/common": "^19.2.7", "@angular/compiler": "^19.2.7", "@angular/core": "^19.2.7", "@angular/forms": "^19.2.7", "@angular/platform-browser": "^19.2.7", "@angular/platform-browser-dynamic": "^19.2.7", "@angular/router": "^19.2.7", "@snf/design-system-components": "0.1.526", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.8", "@angular/cli": "^19.2.8", "@angular/compiler-cli": "^19.2.7", "@eslint/js": "^9.25.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.2.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.31.0"}}