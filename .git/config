[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://<EMAIL>/SNSF-CH/SNF%20Portal/_git/library-SNF-DesignSystem
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "feature/colors"]
	remote = origin
	merge = refs/heads/feature/colors
[branch "feature/colors-fix"]
	remote = origin
	merge = refs/heads/feature/colors-fix
[branch "feature/verification-frame"]
	remote = origin
	merge = refs/heads/feature/verification-frame
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "feature/verification-frame-component"]
	remote = origin
	merge = refs/heads/feature/verification-frame-component
[branch "feature/responsive-layout"]
	remote = origin
	merge = refs/heads/feature/responsive-layout
[branch "feature/USP-39482-search-bar"]
	remote = origin
	merge = refs/heads/feature/USP-39482-search-bar
[branch "feature/USP-39250-fix-info-icon"]
	remote = origin
	merge = refs/heads/feature/USP-39250-fix-info-icon
[branch "feature/USP-38060-search-bar"]
	remote = origin
	merge = refs/heads/feature/USP-38060-search-bar
[branch "bugfix/refactoring-colors"]
	remote = origin
	merge = refs/heads/bugfix/refactoring-colors
[branch "bugfix/breadcrum"]
	remote = origin
	merge = refs/heads/bugfix/breadcrum
[branch "feature/regactor-shapes"]
	remote = origin
	merge = refs/heads/feature/regactor-shapes
[branch "feature/typography-tokens"]
	remote = origin
	merge = refs/heads/feature/typography-tokens
[branch "feature/USP-39885-angular-wrapper"]
	remote = origin
	merge = refs/heads/feature/USP-39885-angular-wrapper
[branch "feature/USP-39885-wrapper-adjustments"]
	remote = origin
	merge = refs/heads/feature/USP-39885-wrapper-adjustments
[branch "renovate/eslint-plugin-n-17.x"]
	remote = origin
	merge = refs/heads/renovate/eslint-plugin-n-17.x
[branch "renovate/typescript-5.x"]
	remote = origin
	merge = refs/heads/renovate/typescript-5.x
[branch "feature/change-fonts"]
	remote = origin
	merge = refs/heads/feature/change-fonts
[branch "feature/USP-40085-icon-button"]
	remote = origin
	merge = refs/heads/feature/USP-40085-icon-button
[branch "feature/library-fix"]
	remote = origin
	merge = refs/heads/feature/library-fix
[branch "feature/breakpoints"]
	remote = origin
	merge = refs/heads/feature/breakpoints
[branch "feature/accessibility"]
	remote = origin
	merge = refs/heads/feature/accessibility
[branch "feature/color-link"]
	remote = origin
	merge = refs/heads/feature/color-link
[branch "feature/material-themes"]
	remote = origin
	merge = refs/heads/feature/material-themes
[branch "feature/design-system-fixes"]
	remote = origin
	merge = refs/heads/feature/design-system-fixes
[branch "feature/style-dictionary-v4"]
	remote = origin
	merge = refs/heads/feature/style-dictionary-v4
