0000000000000000000000000000000000000000 d3f278cb87029fde9ff32edf3f0c8227af1daed0 frfi <<EMAIL>> 1744574200 +0200	branch: Created from HEAD
d3f278cb87029fde9ff32edf3f0c8227af1daed0 0498f65b0b572282a12159169a5419435700078c frfi <<EMAIL>> 1744574460 +0200	commit: Add deployment configs
0498f65b0b572282a12159169a5419435700078c 8d7cbebb77f510a306b5e5ec93560e79c5f8374a frfi <<EMAIL>> 1745091069 +0200	commit: Remove custom footer and layout fixes
8d7cbebb77f510a306b5e5ec93560e79c5f8374a 12736fc2e75030169f54da5b2dbb5914dc39b7c7 frfi <<EMAIL>> 1745091469 +0200	commit: Add Node collection and related components
12736fc2e75030169f54da5b2dbb5914dc39b7c7 dffca5937ccd6483e89030c3bae8d20529a9cbf7 frfi <<EMAIL>> 1745091553 +0200	commit (amend): Add Node collection and related components
dffca5937ccd6483e89030c3bae8d20529a9cbf7 48849013ffb045a414639773c0c3411b31867d65 frfi <<EMAIL>> 1745092191 +0200	commit: Add Home global configuration and data fetching logic
48849013ffb045a414639773c0c3411b31867d65 dd736894e2d476ceb9fa21806333f0d33360ed7a frfi <<EMAIL>> 1745092470 +0200	commit: Refactor global data fetching functions for improved type safety
dd736894e2d476ceb9fa21806333f0d33360ed7a 75aceedae38327967787e5460eae81b6c5f5a6e5 frfi <<EMAIL>> 1745092526 +0200	commit (amend): Refactor global data fetching functions for improved type safety
75aceedae38327967787e5460eae81b6c5f5a6e5 abf1df161774207c89144091c55e9576479bb514 frfi <<EMAIL>> 1745093631 +0200	commit: Add Lexicon global configuration and data fetching logic
abf1df161774207c89144091c55e9576479bb514 1bfa6570b5fa9a1d4d2fd17621bff60eedb34c39 frfi <<EMAIL>> 1745094025 +0200	commit (amend): Add Lexicon global configuration and data fetching logic
1bfa6570b5fa9a1d4d2fd17621bff60eedb34c39 dbe4ec9f249df964f15446ce4914a8e2c7e4de0e frfi <<EMAIL>> 1745993804 +0200	commit: Update configuration and dependencies for improved build and performance
