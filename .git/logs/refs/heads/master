0000000000000000000000000000000000000000 93549394e68faa2b5f4914161ccdd6f1f096312c frfi <<EMAIL>> 1744525590 +0200	commit (initial): Init project
93549394e68faa2b5f4914161ccdd6f1f096312c 7b3bc0a7a0f6251e86c07da26abc7c8fb58732b5 frfi <<EMAIL>> 1744525625 +0200	commit (amend): Init project
7b3bc0a7a0f6251e86c07da26abc7c8fb58732b5 950a3b7504d9d6bbc4e60e363b25d3763cfd339b frfi <<EMAIL>> 1744526719 +0200	commit: Convert webapp from submodule to regular directory
950a3b7504d9d6bbc4e60e363b25d3763cfd339b fb18f4232d00de0f3a0696003d34353ad7b06322 frfi <<EMAIL>> 1744527461 +0200	commit: Fix sticky footer
fb18f4232d00de0f3a0696003d34353ad7b06322 5d3d68e9c74c7f22e21d02a1fbc48f47b7804fac frfi <<EMAIL>> 1744527613 +0200	commit: Remove Radix Demo Page
5d3d68e9c74c7f22e21d02a1fbc48f47b7804fac 12670978c272194ce1a226414ba51d25448b2fe8 frfi <<EMAIL>> 1744529102 +0200	commit: WIP Countries
12670978c272194ce1a226414ba51d25448b2fe8 502833e628adf1bb048e69f3dad6ba5c04325fdb frfi <<EMAIL>> 1744532384 +0200	commit: WIP Country NAV
502833e628adf1bb048e69f3dad6ba5c04325fdb 4f75c6d486a8a80e68ec13f3b5904b099af755a0 frfi <<EMAIL>> 1744534615 +0200	commit: WIP Country Page
4f75c6d486a8a80e68ec13f3b5904b099af755a0 ae2bee80a3b9581f5473fb8f632aaf6b9fe5e2c3 frfi <<EMAIL>> 1744535654 +0200	commit: WIP Country Page types
ae2bee80a3b9581f5473fb8f632aaf6b9fe5e2c3 1f4ae5104c890769024a09b2dc4c370ec9092581 frfi <<EMAIL>> 1744539701 +0200	commit: Create NavItems for Country Menues
1f4ae5104c890769024a09b2dc4c370ec9092581 7d129351266cf023183e4eba73cb38bf4b612224 frfi <<EMAIL>> 1744573156 +0200	rebase (finish): refs/heads/master onto 5d3d68e9c74c7f22e21d02a1fbc48f47b7804fac
7d129351266cf023183e4eba73cb38bf4b612224 d3f278cb87029fde9ff32edf3f0c8227af1daed0 frfi <<EMAIL>> 1744574068 +0200	commit: Add link to dropdown items
