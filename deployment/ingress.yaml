apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  name: geopolitica
  namespace: geopolitica-prod
spec:
  ingressClassName: nginx
  rules:
    - host: geopolitica.ch
      http:
        paths:
          - backend:
              service:
                name: geopolitica-webapp
                port:
                  number: 3000
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - www.geopolitica.ch
        - geopolitica.ch
      secretName: geopolitica-tls