apiVersion: acid.zalan.do/v1
kind: postgresql
metadata:
  name: geopolitica-webapp-db
  namespace: geopolitica-prod
spec:
  databases:
    geopoliticaWebapp: geopoliticaWebapp
  numberOfInstances: 1
  postgresql:
    version: "14"
  resources:
    limits:
      cpu: 150m
      memory: 250M
    requests:
      cpu: 50m
      memory: 150M
  teamId: geopolitica
  users:
    geopolitica:
      - superuser
      - createdb
  volume:
    size: 10Gi
    storageClass: longhorn