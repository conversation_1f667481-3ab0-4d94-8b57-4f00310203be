apiVersion: apps/v1
kind: Deployment
metadata:
  name: geopolitica-webapp
  namespace: geopolitica-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: geopolitica-webapp
  template:
    metadata:
      labels:
        app: geopolitica-webapp
    spec:
      imagePullSecrets:
        - name: geopolitica-gitlab-registry-credentials
      containers:
        - name: geopolitica-webapp
          image: registry.gitlab.com/fredicinho/geopolitica/geopolitica-webapp:latest
          imagePullPolicy: Always
          envFrom:
          - configMapRef:
              name: geopolitica-webapp-config
          ports:
            - containerPort: 3000