# ADR-0: SCS scoped documentation

## Context

We have a lot of documentation in [Confluence](https://snsf-ch.atlassian.net/wiki/spaces/SP/pages/2664530039/Development) which is quite far away from the daily development business. And more on a global level. What is missing is a more fine-grained documentation which is more close to the code and the SCS.

## Decision

We will use an in code documentation per SCS.

## Status

accepted

## Consequences

Every SCS and service will have its own documentation. Which also can be moved along when we give up our mono rep.