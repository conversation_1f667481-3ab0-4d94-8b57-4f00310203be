# ADR-1: Material 3 integration

## Context

Material 3 has currently a hardcoded logic how to assign colors from the theme palettes into the color roles to be used further in the components. Since those colors do not meet our Desing System, we need a way how to configure material so that it takes the colors our design system provides for every role, without having to customize Angular Material

## Decision

We will configure material not only providing the color palettes, but also the color roles through system variables (there is no documentation about it at the moment since it's a [fresh feature](https://github.com/angular/components/issues/28931) from Material 3). This fit totally to our architecture because we provide all these design tokens in Figma and the system variables correspond to the [material design spec](https://m3.material.io/foundations/design-tokens/how-to-read-tokens#20829697-fd3d-4802-b295-96ba564f2e50)

## Status

accepted

## Consequences

We have to configure angular material 3 slightly more difficult, becasue we have to provide the design tokens ourself. A SASS function was created to provide these tokens in every SCS from the design system in order to facilitate this process.
