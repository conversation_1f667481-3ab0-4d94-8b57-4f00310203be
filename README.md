# SNF Design System

This is the SNF Design System containing the design tokens and components to be used in the SNF Portal. It is based on the [Zuehlke Starter Template](https://github.com/Z<PERSON>hlke/design-system-starter).

* GIT Repo: [https://dev.azure.com/SNSF-CH/SNF%20Portal/_git/library-SNF-DesignSystem](https://dev.azure.com/SNSF-CH/SNF%20Portal/_git/library-SNF-DesignSystem)
* Storybook: [https://portal-dev.snf.ch/storybook/design-system/](https://portal-dev.snf.ch/storybook/design-system/)
* Figma: [https://www.figma.com/design/o3pMzfdd4EBE9AaGDdc2uv/Design-System-Styles](https://www.figma.com/design/o3pMzfdd4EBE9AaGDdc2uv/Design-System-Styles)

It consists of two packages:
* `@snf/design-system-components`: The web components library containing the actual components and styles
* `@snf/design-system-components-wrapper`: An Angular wrapper library that provides Angular components that wrap the web components. This allows to use the components in Angular applications with proper typing and Angular-friendly APIs.


## Installation

To use those packages install it via npm:

```bash
npm install @snf/design-system-components
npm install @snf/design-system-components-wrapper
```

Additionally, to set up the proper CSS variables, you need to import the provided `style.scss` file in your project.

```css
@import "@snf/design-system-components/style";
```

## Usage

### Web Components and Styles

To use all components, you can import the `index.js` file in your project:

```ts
import '@snf/design-system-components';
```

If you want to lazy load the components where you need them, you can import the individual components:

```ts
import '@snf/design-system-components/src/components/button/button.component';
```

To use specific functions/mixins in your project, you can import the `style.scss` file.

```css
@use "@snf/design-system-components/style" as snf;

.my-component {
  color: snf.color-scheme(primary);
  margin: snf.spacing(2);
}
```

### Angular Integration

Import the module in your Angular application:

```typescript
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { DesignSystemComponentsWrapperModule } from '@snf/design-system-components-wrapper';

import { AppComponent } from './app.component';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    DesignSystemComponentsWrapperModule
  ],
  bootstrap: [AppComponent]
})
export class AppModule {}
```

Then use the components with the `snf-library-` prefix:

```html
<snf-library-callout>
  This is a callout message
</snf-library-callout>

<snf-library-link-button [href]="https://example.com" [underlined]="true">
  Click me
</snf-library-link-button>
```

For more details on the Angular wrapper, see the [Angular wrapper documentation](./src/angular-wrapper/README.md).

## Development

### Initial setup

Check out the Git repository and run the following commands in the root directory to get started:

```bash
cd src
npm install
npm run build --workspaces
npm run storybook --workspaces
````

### Design tokens

Design tokens are defined in JSON files located in the folder `src/web-components/tokens/json`. The SNF design system uses [Style Dictionary](https://amzn.github.io/style-dictionary/#/) to transform the design token into JavaScript, CSS and JSON.

An example of a design token would be:

```json
{
  "color": {
    "primary": {
      "primary-100": {
        "description": "",
        "type": "color",
        "value": "#E6EAEC"
      },
      "primary-200": {
        "description": "",
        "type": "color",
        "value": "#C3CCD0"
      }
    }
  }
}
```

Run `npm run --workspace web-components design-tokens:generate` to transform the design tokens to the configured formats.

### Local Development with SCS
To be able to work on the design system locally in an SCS project, you can link this library with npm.
First you have to build and "publish" the library locally:
```bash
# @snf/design-system-components
cd src
npm run build:link
```

Then you can link the library in your SCS project:
```bash
# SCS project
npm link @snf/design-system-components
```

If you change something in the library, you have to build it again to be able to use the changes in the SCS project.
The built artefact in the "dist" folder is linked to the SCS project.

To unlink the library from the SCS project:
```bash
# SCS project
npm uninstall --no-save @snf/design-system-components && npm install @snf/design-system-components
```

### Publish Development Web Component
To publish an NPM package of your dedicated development branch do the following
* Open pipeline of Design System Components build & publish and click on **Run Pipeline**
* Select your feature git branch and check **Publish Nuget** and **Run**
* Open the Design System Components feed in Azure Portal Artefacts
* Select your previously built version and run the npm install command in your SCS project
``` bash
# Example npm install command
npm install @snf/design-system-components@0.1.326-usp-37287-ds-bui0001
```

### Known issues during implementation
* Shadow root is missing in DOM of storybook or SCS: check your import statements to also contain a full import of the component
``` bash
# stories.ts
import ReadOnlyRadio from "./readonly-radio.component";
import "./readonly-radio.component";
```

``` bash
# SCS wrapper module
import '@snf/design-system-components/src/components/readonly-radio/readonly-radio.component';
import { ReadOnlyRadioComponent } from './components/readonly-radio/readonly-radio.component';
```



