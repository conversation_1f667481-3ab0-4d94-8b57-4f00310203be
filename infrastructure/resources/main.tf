locals {
  workload            = "snfportal_designsystem"
  workload_short_flat = "snfportalds"
  name                = "Design System"
}

resource "azurerm_resource_group" "rg" {
  location = var.location
  name     = "rg-${local.workload}-${var.environment}"
  tags     = var.tags
}


module "widgets" {
  source              = "app.terraform.io/SNSF-Portal/staticfrontend/azurerm"
  version             = "1.14.0"
  environment         = var.environment
  location            = var.location
  resource_group_name = azurerm_resource_group.rg.name
  name                = local.name
  tags                = var.tags
  url_path            = "/storybook/design-system"
  workload            = local.workload
  workload_short      = local.workload_short_flat
  deployment_agent_id = var.deployment_agent_id
  index_document      = "index.html"
  health_check_path   = "/index.json"
}
