.common-docker-build-webapp:
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}/${TYPE}"
      --dockerfile "${CI_PROJECT_DIR}/${TYPE}/Dockerfile"
      --destination "$DOCKER_REGISTRY/geopolitica-${TYPE}:$NEW_TAG"
      --single-snapshot
    - echo "Build successfully!"
    - echo "WEBAPP_BUILD_CREATED=yes" >> $CI_PROJECT_DIR/build.env
  artifacts:
    reports:
      dotenv: build.env
