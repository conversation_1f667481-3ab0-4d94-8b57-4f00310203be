parameters:
- name: directory
  type: string
- name: module
  type: string
- name: serviceConnection
  type: string
- name: useExistingPlan
  type: boolean
  default: true
- name: stage
  type: string
  default: ''
- name: addCredentials
  type: boolean
  default: false
- name: action
  values:
    - validate
    - plan
    - apply

steps:
- task: TerraformInstaller@1
  displayName: 'terraform install'
  inputs:
    terraformVersion: '1.7.2'

## REQUIRED for manual az cli tasks in Terraform - like Frontdoor routing rule mapping
- ${{ if and(ne(parameters.action, 'validate'), eq(parameters.addCredentials, 'true')) }}:
  - task: AzureCLI@2
    displayName: 'Prepare credentials'
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      addSpnToEnvironment: true
      inlineScript: |
        $env:ARM_CLIENT_ID = $env:servicePrincipalId
        $env:ARM_CLIENT_SECRET = $env:servicePrincipalKey
        $env:ARM_TENANT_ID = $env:tenantId

        Write-Host "##vso[task.setvariable variable=ARM_CLIENT_ID;issecret=false]$env:servicePrincipalId"
        Write-Host "##vso[task.setvariable variable=ARM_CLIENT_SECRET;issecret=true]$env:servicePrincipalKey"
        Write-Host "##vso[task.setvariable variable=ARM_TENANT_ID;issecret=false]$env:tenantId"

        Write-Host "##vso[task.setvariable variable=MSSQL_CLIENT_ID;issecret=false]$env:servicePrincipalId"
        Write-Host "##vso[task.setvariable variable=MSSQL_CLIENT_SECRET;issecret=true]$env:servicePrincipalKey"
        Write-Host "##vso[task.setvariable variable=MSSQL_TENANT_ID;issecret=false]$env:tenantId"
  - pwsh: |
      az login --service-principal --username "$(ARM_CLIENT_ID)" --password "$(ARM_CLIENT_SECRET)" --tenant "$(ARM_TENANT_ID)"
    displayName: 'Login to Azure'
    env:
      ARM_CLIENT_SECRET: "$(ARM_CLIENT_SECRET)"

- task: TerraformTaskV4@4
  displayName: 'terraform init ${{ parameters.stage }}'
  env:
    TF_TOKEN_app_terraform_io: "$(TF_TOKEN_app_terraform_io)"
  inputs:
    provider: 'azurerm'
    command: 'init'
    workingDirectory: ${{ parameters.directory }}
    commandOptions: '-input=false'
    backendServiceArm: ${{ parameters.serviceConnection }}
    backendAzureRmResourceGroupName: $(BackendAzureRmResourceGroupName)
    backendAzureRmStorageAccountName: $(BackendAzureRmStorageAccountName)
    backendAzureRmContainerName: $(BackendAzureRmContainerName)
    backendAzureRmKey: '${{ parameters.module }}.tfstate'

- ${{ if eq(parameters.action, 'validate') }}:
  - template: 'steps-to-inspect-code.yaml'
    parameters:
      workingDirectory: ${{ parameters.directory }}

  - template: 'steps-to-inspect-terraform.yaml'
    parameters:
      workingDirectory: ${{ parameters.directory }}
      tfLintDirectory: $(Build.SourcesDirectory)
      description: ${{ parameters.stage }}

- ${{ if and(eq(parameters.action, 'apply'), eq(parameters.useExistingPlan, true) ) }}:
  - task: DownloadPipelineArtifact@2
    displayName: 'Download terraform plan'
    inputs:
      targetPath: '${{ parameters.directory }}'
      artifact: 'tfplan-${{ parameters.stage }}-$(System.StageAttempt)'
      publishLocation: 'pipeline'

- task: TerraformTaskV4@4
  displayName: 'terraform ${{ parameters.action }} ${{ parameters.stage }}'
  name: 'terraform${{ parameters.action }}${{ parameters.stage }}'
  env:
    ARM_CLIENT_SECRET: "$(ARM_CLIENT_SECRET)"
    MSSQL_CLIENT_SECRET: "$(MSSQL_CLIENT_SECRET)"
    TF_TOKEN_app_terraform_io: "$(TF_TOKEN_app_terraform_io)"
  inputs:
    provider: 'azurerm'
    command: ${{ parameters.action }}
    workingDirectory: ${{ parameters.directory }}
    ${{ if eq(parameters.action, 'validate') }}:
      commandOptions: ''
    ${{ if eq(parameters.action, 'plan') }}:
      commandOptions: '-input=false -out $(Build.ArtifactStagingDirectory)/main.tfplan'
    ${{ if eq(parameters.action, 'apply') }}:
      ${{ if eq(parameters.useExistingPlan, true) }}:
        commandOptions: 'main.tfplan'
      ${{ if eq(parameters.useExistingPlan, false) }}:
        commandOptions: '-input=false -auto-approve'
    environmentServiceNameAzureRM: ${{ parameters.serviceConnection }}

- ${{ if eq(parameters.action, 'plan') }}:
  - task: PublishPipelineArtifact@1
    displayName: 'Upload terraform plan'
    continueOnError: true
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/main.tfplan'
      artifact: 'tfplan-${{ parameters.stage }}-$(System.StageAttempt)'
      publishLocation: 'pipeline'
