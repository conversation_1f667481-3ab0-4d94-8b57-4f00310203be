parameters:
  - name: stage
    type: string
  - name: serviceConnection
    type: string

steps:
  # Purge front door cache
  - task: AzureCLI@2
    displayName: 'Purge front door cache'
    continueOnError: true
    timeoutInMinutes: 5
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
        $domains = if ("${{ lower(parameters.stage) }}" -eq "prod") { @('portal.snf.ch','portal.snsf.ch','portal.fns.ch') } else { @('portal-${{ lower(parameters.stage) }}.snf.ch','portal-${{ lower(parameters.stage) }}.fns.ch','portal-${{ lower(parameters.stage) }}.snsf.ch') }
        $cleanupcommand = "az afd endpoint purge --resource-group rg-snfportal_shared-${{ parameters.stage }} --profile-name afd-snfportal-shared-${{ parameters.stage }} --endpoint-name fd-snfportal-shared-${{ parameters.stage }} --domains $domains --content-paths `"/*`" --no-wait"

        # Show the command in output to verify the parameters
        Write-Host $cleanupcommand

        # Show the used subscription
        az account show

        # Execute the command
        Invoke-Expression $cleanupcommand

        # Done
        Write-Host 'Front door cache purging started'