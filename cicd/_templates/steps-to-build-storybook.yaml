parameters:
  - name: workingDirectory
    type: string
  - name: project
    type: string

steps:
  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Build-Storybook'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} build-storybook'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: PublishPipelineArtifact@1
    displayName: '${{ parameters.project }}: Publish NPM package to pipeline'
    inputs:
      targetPath: '${{ parameters.workingDirectory }}/${{ parameters.project }}/storybook-static'
      artifact: '${{ parameters.project }}-storybook'
      publishLocation: 'pipeline'
