parameters:
  - name: workingDirectory
    type: string
  - name: project
    type: string

steps:
  - task: Cache@2
    inputs:
      key: 'npm | "$(Agent.OS)" | "${{ parameters.workingDirectory }}/package-lock.json"'
      restoreKeys: |
        npm | "$(Agent.OS)"
      path: $(npm_config_cache)
    displayName: Cache npm

  - task: npmAuthenticate@0
    displayName: '${{ parameters.project }}: NPM Auth'
    inputs:
      workingFile: ${{ parameters.workingDirectory }}/.npmrc

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Install'
    inputs:
      command: ci
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Lint'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} lint'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: Update web-components peer dependency version'
    inputs:
      command: custom
      customCommand: 'pkg set "peerDependencies.@snf/design-system-components=$(NuGetVersionV2)"'
      workingDir: '${{ parameters.workingDirectory }}/${{ parameters.project }}/projects/design-system-wrapper'

  - task: Npm@1
    displayName: '${{ parameters.project }}: NPM Run Build'
    inputs:
      command: custom
      customCommand: 'run --workspace ${{parameters.project}} build'
      workingDir: '${{ parameters.workingDirectory }}'

  - task: Npm@1
    displayName: '${{ parameters.project }}: Write NPM Package Version'
    inputs:
      command: custom
      customCommand: 'version $(NuGetVersionV2)'
      workingDir: '${{ parameters.workingDirectory }}/${{ parameters.project }}/dist/design-system-wrapper'

  - task: PublishPipelineArtifact@1
    displayName: '${{ parameters.project }}: Publish NPM package to pipeline'
    inputs:
      targetPath: '${{ parameters.workingDirectory }}/${{ parameters.project }}/dist/design-system-wrapper'
      artifact: '${{ parameters.project }}-npm'
      publishLocation: 'pipeline'
